"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fingerprintjs";
exports.ids = ["vendor-chunks/@fingerprintjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js":
/*!******************************************************************!*\
  !*** ./node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   componentsToDebugString: () => (/* binding */ componentsToDebugString),\n/* harmony export */   \"default\": () => (/* binding */ index),\n/* harmony export */   getFullscreenElement: () => (/* binding */ getFullscreenElement),\n/* harmony export */   getUnstableAudioFingerprint: () => (/* binding */ getUnstableAudioFingerprint),\n/* harmony export */   getUnstableCanvasFingerprint: () => (/* binding */ getUnstableCanvasFingerprint),\n/* harmony export */   getUnstableScreenFrame: () => (/* binding */ getUnstableScreenFrame),\n/* harmony export */   getUnstableScreenResolution: () => (/* binding */ getUnstableScreenResolution),\n/* harmony export */   getWebGLContext: () => (/* binding */ getWebGLContext),\n/* harmony export */   hashComponents: () => (/* binding */ hashComponents),\n/* harmony export */   isAndroid: () => (/* binding */ isAndroid),\n/* harmony export */   isChromium: () => (/* binding */ isChromium),\n/* harmony export */   isDesktopWebKit: () => (/* binding */ isDesktopWebKit),\n/* harmony export */   isEdgeHTML: () => (/* binding */ isEdgeHTML),\n/* harmony export */   isGecko: () => (/* binding */ isGecko),\n/* harmony export */   isSamsungInternet: () => (/* binding */ isSamsungInternet),\n/* harmony export */   isTrident: () => (/* binding */ isTrident),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit),\n/* harmony export */   load: () => (/* binding */ load),\n/* harmony export */   loadSources: () => (/* binding */ loadSources),\n/* harmony export */   murmurX64Hash128: () => (/* binding */ murmurX64Hash128),\n/* harmony export */   prepareForSources: () => (/* binding */ prepareForSources),\n/* harmony export */   sources: () => (/* binding */ sources),\n/* harmony export */   transformSource: () => (/* binding */ transformSource),\n/* harmony export */   withIframe: () => (/* binding */ withIframe)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/**\n * FingerprintJS v4.6.2 - Copyright (c) FingerprintJS, Inc, 2025 (https://fingerprint.com)\n *\n * Licensed under Business Source License 1.1 https://mariadb.com/bsl11/\n * Licensor: FingerprintJS, Inc.\n * Licensed Work: FingerprintJS browser fingerprinting library\n * Additional Use Grant: None\n * Change Date: Four years from first release for the specific version.\n * Change License: MIT, text at https://opensource.org/license/mit/ with the following copyright notice:\n * Copyright 2015-present FingerprintJS, Inc.\n */\n\n\n\nvar version = \"4.6.2\";\n\nfunction wait(durationMs, resolveWith) {\n    return new Promise(function (resolve) { return setTimeout(resolve, durationMs, resolveWith); });\n}\n/**\n * Allows asynchronous actions and microtasks to happen.\n */\nfunction releaseEventLoop() {\n    // Don't use setTimeout because Chrome throttles it in some cases causing very long agent execution:\n    // https://stackoverflow.com/a/6032591/1118709\n    // https://github.com/chromium/chromium/commit/0295dd09496330f3a9103ef7e543fa9b6050409b\n    // Reusing a MessageChannel object gives no noticeable benefits\n    return new Promise(function (resolve) {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function () { return resolve(); };\n        channel.port2.postMessage(null);\n    });\n}\nfunction requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {\n    if (deadlineTimeout === void 0) { deadlineTimeout = Infinity; }\n    var requestIdleCallback = window.requestIdleCallback;\n    if (requestIdleCallback) {\n        // The function `requestIdleCallback` loses the binding to `window` here.\n        // `globalThis` isn't always equal `window` (see https://github.com/fingerprintjs/fingerprintjs/issues/683).\n        // Therefore, an error can occur. `call(window,` prevents the error.\n        return new Promise(function (resolve) { return requestIdleCallback.call(window, function () { return resolve(); }, { timeout: deadlineTimeout }); });\n    }\n    else {\n        return wait(Math.min(fallbackTimeout, deadlineTimeout));\n    }\n}\nfunction isPromise(value) {\n    return !!value && typeof value.then === 'function';\n}\n/**\n * Calls a maybe asynchronous function without creating microtasks when the function is synchronous.\n * Catches errors in both cases.\n *\n * If just you run a code like this:\n * ```\n * console.time('Action duration')\n * await action()\n * console.timeEnd('Action duration')\n * ```\n * The synchronous function time can be measured incorrectly because another microtask may run before the `await`\n * returns the control back to the code.\n */\nfunction awaitIfAsync(action, callback) {\n    try {\n        var returnedValue = action();\n        if (isPromise(returnedValue)) {\n            returnedValue.then(function (result) { return callback(true, result); }, function (error) { return callback(false, error); });\n        }\n        else {\n            callback(true, returnedValue);\n        }\n    }\n    catch (error) {\n        callback(false, error);\n    }\n}\n/**\n * If you run many synchronous tasks without using this function, the JS main loop will be busy and asynchronous tasks\n * (e.g. completing a network request, rendering the page) won't be able to happen.\n * This function allows running many synchronous tasks such way that asynchronous tasks can run too in background.\n */\nfunction mapWithBreaks(items, callback, loopReleaseInterval) {\n    if (loopReleaseInterval === void 0) { loopReleaseInterval = 16; }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var results, lastLoopReleaseTime, i, now;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    results = Array(items.length);\n                    lastLoopReleaseTime = Date.now();\n                    i = 0;\n                    _a.label = 1;\n                case 1:\n                    if (!(i < items.length)) return [3 /*break*/, 4];\n                    results[i] = callback(items[i], i);\n                    now = Date.now();\n                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval)) return [3 /*break*/, 3];\n                    lastLoopReleaseTime = now;\n                    return [4 /*yield*/, releaseEventLoop()];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    ++i;\n                    return [3 /*break*/, 1];\n                case 4: return [2 /*return*/, results];\n            }\n        });\n    });\n}\n/**\n * Makes the given promise never emit an unhandled promise rejection console warning.\n * The promise will still pass errors to the next promises.\n * Returns the input promise for convenience.\n *\n * Otherwise, promise emits a console warning unless it has a `catch` listener.\n */\nfunction suppressUnhandledRejectionWarning(promise) {\n    promise.then(undefined, function () { return undefined; });\n    return promise;\n}\n\n/*\n * This file contains functions to work with pure data only (no browser features, DOM, side effects, etc).\n */\n/**\n * Does the same as Array.prototype.includes but has better typing\n */\nfunction includes(haystack, needle) {\n    for (var i = 0, l = haystack.length; i < l; ++i) {\n        if (haystack[i] === needle) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Like `!includes()` but with proper typing\n */\nfunction excludes(haystack, needle) {\n    return !includes(haystack, needle);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toInt(value) {\n    return parseInt(value);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toFloat(value) {\n    return parseFloat(value);\n}\nfunction replaceNaN(value, replacement) {\n    return typeof value === 'number' && isNaN(value) ? replacement : value;\n}\nfunction countTruthy(values) {\n    return values.reduce(function (sum, value) { return sum + (value ? 1 : 0); }, 0);\n}\nfunction round(value, base) {\n    if (base === void 0) { base = 1; }\n    if (Math.abs(base) >= 1) {\n        return Math.round(value / base) * base;\n    }\n    else {\n        // Sometimes when a number is multiplied by a small number, precision is lost,\n        // for example 1234 * 0.0001 === 0.12340000000000001, and it's more precise divide: 1234 / (1 / 0.0001) === 0.1234.\n        var counterBase = 1 / base;\n        return Math.round(value * counterBase) / counterBase;\n    }\n}\n/**\n * Parses a CSS selector into tag name with HTML attributes.\n * Only single element selector are supported (without operators like space, +, >, etc).\n *\n * Multiple values can be returned for each attribute. You decide how to handle them.\n */\nfunction parseSimpleCssSelector(selector) {\n    var _a, _b;\n    var errorMessage = \"Unexpected syntax '\".concat(selector, \"'\");\n    var tagMatch = /^\\s*([a-z-]*)(.*)$/i.exec(selector);\n    var tag = tagMatch[1] || undefined;\n    var attributes = {};\n    var partsRegex = /([.:#][\\w-]+|\\[.+?\\])/gi;\n    var addAttribute = function (name, value) {\n        attributes[name] = attributes[name] || [];\n        attributes[name].push(value);\n    };\n    for (;;) {\n        var match = partsRegex.exec(tagMatch[2]);\n        if (!match) {\n            break;\n        }\n        var part = match[0];\n        switch (part[0]) {\n            case '.':\n                addAttribute('class', part.slice(1));\n                break;\n            case '#':\n                addAttribute('id', part.slice(1));\n                break;\n            case '[': {\n                var attributeMatch = /^\\[([\\w-]+)([~|^$*]?=(\"(.*?)\"|([\\w-]+)))?(\\s+[is])?\\]$/.exec(part);\n                if (attributeMatch) {\n                    addAttribute(attributeMatch[1], (_b = (_a = attributeMatch[4]) !== null && _a !== void 0 ? _a : attributeMatch[5]) !== null && _b !== void 0 ? _b : '');\n                }\n                else {\n                    throw new Error(errorMessage);\n                }\n                break;\n            }\n            default:\n                throw new Error(errorMessage);\n        }\n    }\n    return [tag, attributes];\n}\n/**\n * Converts a string to UTF8 bytes\n */\nfunction getUTF8Bytes(input) {\n    // Benchmark: https://jsbench.me/b6klaaxgwq/1\n    // If you want to just count bytes, see solutions at https://jsbench.me/ehklab415e/1\n    var result = new Uint8Array(input.length);\n    for (var i = 0; i < input.length; i++) {\n        // `charCode` is faster than encoding, so we prefer that when it's possible\n        var charCode = input.charCodeAt(i);\n        // In case of non-ASCII symbols we use proper encoding\n        if (charCode > 127) {\n            return new TextEncoder().encode(input);\n        }\n        result[i] = charCode;\n    }\n    return result;\n}\n\n/*\n * Based on https://github.com/karanlyons/murmurHash3.js/blob/a33d0723127e2e5415056c455f8aed2451ace208/murmurHash3.js\n */\n/**\n * Adds two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Add(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 + n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 + n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 + n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 + n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Multiplies two 64-bit values (provided as tuples of 32-bit values)\n * and updates (mutates) first value to write the result\n */\nfunction x64Multiply(m, n) {\n    var m0 = m[0] >>> 16, m1 = m[0] & 0xffff, m2 = m[1] >>> 16, m3 = m[1] & 0xffff;\n    var n0 = n[0] >>> 16, n1 = n[0] & 0xffff, n2 = n[1] >>> 16, n3 = n[1] & 0xffff;\n    var o0 = 0, o1 = 0, o2 = 0, o3 = 0;\n    o3 += m3 * n3;\n    o2 += o3 >>> 16;\n    o3 &= 0xffff;\n    o2 += m2 * n3;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o2 += m3 * n2;\n    o1 += o2 >>> 16;\n    o2 &= 0xffff;\n    o1 += m1 * n3;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m2 * n2;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o1 += m3 * n1;\n    o0 += o1 >>> 16;\n    o1 &= 0xffff;\n    o0 += m0 * n3 + m1 * n2 + m2 * n1 + m3 * n0;\n    o0 &= 0xffff;\n    m[0] = (o0 << 16) | o1;\n    m[1] = (o2 << 16) | o3;\n}\n/**\n * Provides left rotation of the given int64 value (provided as tuple of two int32)\n * by given number of bits. Result is written back to the value\n */\nfunction x64Rotl(m, bits) {\n    var m0 = m[0];\n    bits %= 64;\n    if (bits === 32) {\n        m[0] = m[1];\n        m[1] = m0;\n    }\n    else if (bits < 32) {\n        m[0] = (m0 << bits) | (m[1] >>> (32 - bits));\n        m[1] = (m[1] << bits) | (m0 >>> (32 - bits));\n    }\n    else {\n        bits -= 32;\n        m[0] = (m[1] << bits) | (m0 >>> (32 - bits));\n        m[1] = (m0 << bits) | (m[1] >>> (32 - bits));\n    }\n}\n/**\n * Provides a left shift of the given int32 value (provided as tuple of [0, int32])\n * by given number of bits. Result is written back to the value\n */\nfunction x64LeftShift(m, bits) {\n    bits %= 64;\n    if (bits === 0) {\n        return;\n    }\n    else if (bits < 32) {\n        m[0] = m[1] >>> (32 - bits);\n        m[1] = m[1] << bits;\n    }\n    else {\n        m[0] = m[1] << (bits - 32);\n        m[1] = 0;\n    }\n}\n/**\n * Provides a XOR of the given int64 values(provided as tuple of two int32).\n * Result is written back to the first value\n */\nfunction x64Xor(m, n) {\n    m[0] ^= n[0];\n    m[1] ^= n[1];\n}\nvar F1 = [0xff51afd7, 0xed558ccd];\nvar F2 = [0xc4ceb9fe, 0x1a85ec53];\n/**\n * Calculates murmurHash3's final x64 mix of that block and writes result back to the input value.\n * (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n * only place where we need to right shift 64bit ints.)\n */\nfunction x64Fmix(h) {\n    var shifted = [0, h[0] >>> 1];\n    x64Xor(h, shifted);\n    x64Multiply(h, F1);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n    x64Multiply(h, F2);\n    shifted[1] = h[0] >>> 1;\n    x64Xor(h, shifted);\n}\nvar C1 = [0x87c37b91, 0x114253d5];\nvar C2 = [0x4cf5ad43, 0x2745937f];\nvar M$1 = [0, 5];\nvar N1 = [0, 0x52dce729];\nvar N2 = [0, 0x38495ab5];\n/**\n * Given a string and an optional seed as an int, returns a 128 bit\n * hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n * All internal functions mutates passed value to achieve minimal memory allocations and GC load\n *\n * Benchmark https://jsbench.me/p4lkpaoabi/1\n */\nfunction x64hash128(input, seed) {\n    var key = getUTF8Bytes(input);\n    seed = seed || 0;\n    var length = [0, key.length];\n    var remainder = length[1] % 16;\n    var bytes = length[1] - remainder;\n    var h1 = [0, seed];\n    var h2 = [0, seed];\n    var k1 = [0, 0];\n    var k2 = [0, 0];\n    var i;\n    for (i = 0; i < bytes; i = i + 16) {\n        k1[0] = key[i + 4] | (key[i + 5] << 8) | (key[i + 6] << 16) | (key[i + 7] << 24);\n        k1[1] = key[i] | (key[i + 1] << 8) | (key[i + 2] << 16) | (key[i + 3] << 24);\n        k2[0] = key[i + 12] | (key[i + 13] << 8) | (key[i + 14] << 16) | (key[i + 15] << 24);\n        k2[1] = key[i + 8] | (key[i + 9] << 8) | (key[i + 10] << 16) | (key[i + 11] << 24);\n        x64Multiply(k1, C1);\n        x64Rotl(k1, 31);\n        x64Multiply(k1, C2);\n        x64Xor(h1, k1);\n        x64Rotl(h1, 27);\n        x64Add(h1, h2);\n        x64Multiply(h1, M$1);\n        x64Add(h1, N1);\n        x64Multiply(k2, C2);\n        x64Rotl(k2, 33);\n        x64Multiply(k2, C1);\n        x64Xor(h2, k2);\n        x64Rotl(h2, 31);\n        x64Add(h2, h1);\n        x64Multiply(h2, M$1);\n        x64Add(h2, N2);\n    }\n    k1[0] = 0;\n    k1[1] = 0;\n    k2[0] = 0;\n    k2[1] = 0;\n    var val = [0, 0];\n    switch (remainder) {\n        case 15:\n            val[1] = key[i + 14];\n            x64LeftShift(val, 48);\n            x64Xor(k2, val);\n        // fallthrough\n        case 14:\n            val[1] = key[i + 13];\n            x64LeftShift(val, 40);\n            x64Xor(k2, val);\n        // fallthrough\n        case 13:\n            val[1] = key[i + 12];\n            x64LeftShift(val, 32);\n            x64Xor(k2, val);\n        // fallthrough\n        case 12:\n            val[1] = key[i + 11];\n            x64LeftShift(val, 24);\n            x64Xor(k2, val);\n        // fallthrough\n        case 11:\n            val[1] = key[i + 10];\n            x64LeftShift(val, 16);\n            x64Xor(k2, val);\n        // fallthrough\n        case 10:\n            val[1] = key[i + 9];\n            x64LeftShift(val, 8);\n            x64Xor(k2, val);\n        // fallthrough\n        case 9:\n            val[1] = key[i + 8];\n            x64Xor(k2, val);\n            x64Multiply(k2, C2);\n            x64Rotl(k2, 33);\n            x64Multiply(k2, C1);\n            x64Xor(h2, k2);\n        // fallthrough\n        case 8:\n            val[1] = key[i + 7];\n            x64LeftShift(val, 56);\n            x64Xor(k1, val);\n        // fallthrough\n        case 7:\n            val[1] = key[i + 6];\n            x64LeftShift(val, 48);\n            x64Xor(k1, val);\n        // fallthrough\n        case 6:\n            val[1] = key[i + 5];\n            x64LeftShift(val, 40);\n            x64Xor(k1, val);\n        // fallthrough\n        case 5:\n            val[1] = key[i + 4];\n            x64LeftShift(val, 32);\n            x64Xor(k1, val);\n        // fallthrough\n        case 4:\n            val[1] = key[i + 3];\n            x64LeftShift(val, 24);\n            x64Xor(k1, val);\n        // fallthrough\n        case 3:\n            val[1] = key[i + 2];\n            x64LeftShift(val, 16);\n            x64Xor(k1, val);\n        // fallthrough\n        case 2:\n            val[1] = key[i + 1];\n            x64LeftShift(val, 8);\n            x64Xor(k1, val);\n        // fallthrough\n        case 1:\n            val[1] = key[i];\n            x64Xor(k1, val);\n            x64Multiply(k1, C1);\n            x64Rotl(k1, 31);\n            x64Multiply(k1, C2);\n            x64Xor(h1, k1);\n        // fallthrough\n    }\n    x64Xor(h1, length);\n    x64Xor(h2, length);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    x64Fmix(h1);\n    x64Fmix(h2);\n    x64Add(h1, h2);\n    x64Add(h2, h1);\n    return (('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8));\n}\n\n/**\n * Converts an error object to a plain object that can be used with `JSON.stringify`.\n * If you just run `JSON.stringify(error)`, you'll get `'{}'`.\n */\nfunction errorToObject(error) {\n    var _a;\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({ name: error.name, message: error.message, stack: (_a = error.stack) === null || _a === void 0 ? void 0 : _a.split('\\n') }, error);\n}\nfunction isFunctionNative(func) {\n    return /^function\\s.*?\\{\\s*\\[native code]\\s*}$/.test(String(func));\n}\n\nfunction isFinalResultLoaded(loadResult) {\n    return typeof loadResult !== 'function';\n}\n/**\n * Loads the given entropy source. Returns a function that gets an entropy component from the source.\n *\n * The result is returned synchronously to prevent `loadSources` from\n * waiting for one source to load before getting the components from the other sources.\n */\nfunction loadSource(source, sourceOptions) {\n    var sourceLoadPromise = suppressUnhandledRejectionWarning(new Promise(function (resolveLoad) {\n        var loadStartTime = Date.now();\n        // `awaitIfAsync` is used instead of just `await` in order to measure the duration of synchronous sources\n        // correctly (other microtasks won't affect the duration).\n        awaitIfAsync(source.bind(null, sourceOptions), function () {\n            var loadArgs = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                loadArgs[_i] = arguments[_i];\n            }\n            var loadDuration = Date.now() - loadStartTime;\n            // Source loading failed\n            if (!loadArgs[0]) {\n                return resolveLoad(function () { return ({ error: loadArgs[1], duration: loadDuration }); });\n            }\n            var loadResult = loadArgs[1];\n            // Source loaded with the final result\n            if (isFinalResultLoaded(loadResult)) {\n                return resolveLoad(function () { return ({ value: loadResult, duration: loadDuration }); });\n            }\n            // Source loaded with \"get\" stage\n            resolveLoad(function () {\n                return new Promise(function (resolveGet) {\n                    var getStartTime = Date.now();\n                    awaitIfAsync(loadResult, function () {\n                        var getArgs = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            getArgs[_i] = arguments[_i];\n                        }\n                        var duration = loadDuration + Date.now() - getStartTime;\n                        // Source getting failed\n                        if (!getArgs[0]) {\n                            return resolveGet({ error: getArgs[1], duration: duration });\n                        }\n                        // Source getting succeeded\n                        resolveGet({ value: getArgs[1], duration: duration });\n                    });\n                });\n            });\n        });\n    }));\n    return function getComponent() {\n        return sourceLoadPromise.then(function (finalizeSource) { return finalizeSource(); });\n    };\n}\n/**\n * Loads the given entropy sources. Returns a function that collects the entropy components.\n *\n * The result is returned synchronously in order to allow start getting the components\n * before the sources are loaded completely.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction loadSources(sources, sourceOptions, excludeSources, loopReleaseInterval) {\n    var includedSources = Object.keys(sources).filter(function (sourceKey) { return excludes(excludeSources, sourceKey); });\n    // Using `mapWithBreaks` allows asynchronous sources to complete between synchronous sources\n    // and measure the duration correctly\n    var sourceGettersPromise = suppressUnhandledRejectionWarning(mapWithBreaks(includedSources, function (sourceKey) { return loadSource(sources[sourceKey], sourceOptions); }, loopReleaseInterval));\n    return function getComponents() {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var sourceGetters, componentPromises, componentArray, components, index;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, sourceGettersPromise];\n                    case 1:\n                        sourceGetters = _a.sent();\n                        return [4 /*yield*/, mapWithBreaks(sourceGetters, function (sourceGetter) { return suppressUnhandledRejectionWarning(sourceGetter()); }, loopReleaseInterval)];\n                    case 2:\n                        componentPromises = _a.sent();\n                        return [4 /*yield*/, Promise.all(componentPromises)\n                            // Keeping the component keys order the same as the source keys order\n                        ];\n                    case 3:\n                        componentArray = _a.sent();\n                        components = {};\n                        for (index = 0; index < includedSources.length; ++index) {\n                            components[includedSources[index]] = componentArray[index];\n                        }\n                        return [2 /*return*/, components];\n                }\n            });\n        });\n    };\n}\n/**\n * Modifies an entropy source by transforming its returned value with the given function.\n * Keeps the source properties: sync/async, 1/2 stages.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction transformSource(source, transformValue) {\n    var transformLoadResult = function (loadResult) {\n        if (isFinalResultLoaded(loadResult)) {\n            return transformValue(loadResult);\n        }\n        return function () {\n            var getResult = loadResult();\n            if (isPromise(getResult)) {\n                return getResult.then(transformValue);\n            }\n            return transformValue(getResult);\n        };\n    };\n    return function (options) {\n        var loadResult = source(options);\n        if (isPromise(loadResult)) {\n            return loadResult.then(transformLoadResult);\n        }\n        return transformLoadResult(loadResult);\n    };\n}\n\n/*\n * Functions to help with features that vary through browsers\n */\n/**\n * Checks whether the browser is based on Trident (the Internet Explorer engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isTrident() {\n    var w = window;\n    var n = navigator;\n    // The properties are checked to be in IE 10, IE 11 and not to be in other browsers in October 2020\n    return (countTruthy([\n        'MSCSSMatrix' in w,\n        'msSetImmediate' in w,\n        'msIndexedDB' in w,\n        'msMaxTouchPoints' in n,\n        'msPointerEnabled' in n,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on EdgeHTML (the pre-Chromium Edge engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isEdgeHTML() {\n    // Based on research in October 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy(['msWriteProfilerMark' in w, 'MSStream' in w, 'msLaunchUri' in n, 'msSaveBlob' in n]) >= 3 &&\n        !isTrident());\n}\n/**\n * Checks whether the browser is based on Chromium without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isChromium() {\n    // Based on research in October 2020. Tested to detect Chromium 42-86.\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'webkitPersistentStorage' in n,\n        'webkitTemporaryStorage' in n,\n        (n.vendor || '').indexOf('Google') === 0,\n        'webkitResolveLocalFileSystemURL' in w,\n        'BatteryManager' in w,\n        'webkitMediaStream' in w,\n        'webkitSpeechGrammar' in w,\n    ]) >= 5);\n}\n/**\n * Checks whether the browser is based on mobile or desktop Safari without using user-agent.\n * All iOS browsers use WebKit (the Safari engine).\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isWebKit() {\n    // Based on research in August 2024\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'ApplePayError' in w,\n        'CSSPrimitiveValue' in w,\n        'Counter' in w,\n        n.vendor.indexOf('Apple') === 0,\n        'RGBColor' in w,\n        'WebKitMediaKeys' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is a desktop browser.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isDesktopWebKit() {\n    // Checked in Safari and DuckDuckGo\n    var w = window;\n    var HTMLElement = w.HTMLElement, Document = w.Document;\n    return (countTruthy([\n        'safari' in w,\n        !('ongestureend' in w),\n        !('TouchEvent' in w),\n        !('orientation' in w),\n        HTMLElement && !('autocapitalize' in HTMLElement.prototype),\n        Document && 'pointerLockElement' in Document.prototype,\n    ]) >= 4);\n}\n/**\n * Checks whether this WebKit browser is Safari.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * Warning! The function works properly only for Safari version 15.4 and newer.\n */\nfunction isSafariWebKit() {\n    // Checked in Safari, Chrome, Firefox, Yandex, UC Browser, Opera, Edge and DuckDuckGo.\n    // iOS Safari and Chrome were checked on iOS 11-18. DuckDuckGo was checked on iOS 17-18 and macOS 14-15.\n    // Desktop Safari versions 12-18 were checked.\n    // The other browsers were checked on iOS 17 and 18; there was no chance to check them on the other OS versions.\n    var w = window;\n    return (\n    // Filters-out Chrome, Yandex, DuckDuckGo (macOS and iOS), Edge\n    isFunctionNative(w.print) &&\n        // Doesn't work in Safari < 15.4\n        String(w.browser) === '[object WebPageNamespace]');\n}\n/**\n * Checks whether the browser is based on Gecko (Firefox engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isGecko() {\n    var _a, _b;\n    var w = window;\n    // Based on research in September 2020\n    return (countTruthy([\n        'buildID' in navigator,\n        'MozAppearance' in ((_b = (_a = document.documentElement) === null || _a === void 0 ? void 0 : _a.style) !== null && _b !== void 0 ? _b : {}),\n        'onmozfullscreenchange' in w,\n        'mozInnerScreenX' in w,\n        'CSSMozDocumentRule' in w,\n        'CanvasCaptureMediaStream' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥86 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium86OrNewer() {\n    // Checked in Chrome 85 vs Chrome 86 both on desktop and Android. Checked in macOS Chrome 128, Android Chrome 127.\n    var w = window;\n    return (countTruthy([\n        !('MediaSettingsRange' in w),\n        'RTCEncodedAudioFrame' in w,\n        '' + w.Intl === '[object Intl]',\n        '' + w.Reflect === '[object Reflect]',\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥122 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium122OrNewer() {\n    // Checked in Chrome 121 vs Chrome 122 and 129 both on desktop and Android\n    var w = window;\n    var URLPattern = w.URLPattern;\n    return (countTruthy([\n        'union' in Set.prototype,\n        'Iterator' in w,\n        URLPattern && 'hasRegExpGroups' in URLPattern.prototype,\n        'RGB8' in WebGLRenderingContext.prototype,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥606 (Safari ≥12) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://en.wikipedia.org/wiki/Safari_version_history#Release_history Safari-WebKit versions map\n */\nfunction isWebKit606OrNewer() {\n    // Checked in Safari 9–18\n    var w = window;\n    return (countTruthy([\n        'DOMRectList' in w,\n        'RTCPeerConnectionIceEvent' in w,\n        'SVGGeometryElement' in w,\n        'ontransitioncancel' in w,\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥616 (Safari ≥17) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @see https://developer.apple.com/documentation/safari-release-notes/safari-17-release-notes Safari 17 release notes\n * @see https://tauri.app/v1/references/webview-versions/#webkit-versions-in-safari Safari-WebKit versions map\n */\nfunction isWebKit616OrNewer() {\n    var w = window;\n    var n = navigator;\n    var CSS = w.CSS, HTMLButtonElement = w.HTMLButtonElement;\n    return (countTruthy([\n        !('getStorageUpdates' in n),\n        HTMLButtonElement && 'popover' in HTMLButtonElement.prototype,\n        'CSSCounterStyleRule' in w,\n        CSS.supports('font-size-adjust: ex-height 0.5'),\n        CSS.supports('text-transform: full-width'),\n    ]) >= 4);\n}\n/**\n * Checks whether the device is an iPad.\n * It doesn't check that the engine is WebKit and that the WebKit isn't desktop.\n */\nfunction isIPad() {\n    // Checked on:\n    // Safari on iPadOS (both mobile and desktop modes): 8, 11-18\n    // Chrome on iPadOS (both mobile and desktop modes): 11-18\n    // Safari on iOS (both mobile and desktop modes): 9-18\n    // Chrome on iOS (both mobile and desktop modes): 9-18\n    // Before iOS 13. Safari tampers the value in \"request desktop site\" mode since iOS 13.\n    if (navigator.platform === 'iPad') {\n        return true;\n    }\n    var s = screen;\n    var screenRatio = s.width / s.height;\n    return (countTruthy([\n        // Since iOS 13. Doesn't work in Chrome on iPadOS <15, but works in desktop mode.\n        'MediaSource' in window,\n        // Since iOS 12. Doesn't work in Chrome on iPadOS.\n        !!Element.prototype.webkitRequestFullscreen,\n        // iPhone 4S that runs iOS 9 matches this, but it is not supported\n        // Doesn't work in incognito mode of Safari ≥17 with split screen because of tracking prevention\n        screenRatio > 0.65 && screenRatio < 1.53,\n    ]) >= 2);\n}\n/**\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getFullscreenElement() {\n    var d = document;\n    return d.fullscreenElement || d.msFullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || null;\n}\nfunction exitFullscreen() {\n    var d = document;\n    // `call` is required because the function throws an error without a proper \"this\" context\n    return (d.exitFullscreen || d.msExitFullscreen || d.mozCancelFullScreen || d.webkitExitFullscreen).call(d);\n}\n/**\n * Checks whether the device runs on Android without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isAndroid() {\n    var isItChromium = isChromium();\n    var isItGecko = isGecko();\n    var w = window;\n    var n = navigator;\n    var c = 'connection';\n    // Chrome removes all words \"Android\" from `navigator` when desktop version is requested\n    // Firefox keeps \"Android\" in `navigator.appVersion` when desktop version is requested\n    if (isItChromium) {\n        return (countTruthy([\n            !('SharedWorker' in w),\n            // `typechange` is deprecated, but it's still present on Android (tested on Chrome Mobile 117)\n            // Removal proposal https://bugs.chromium.org/p/chromium/issues/detail?id=699892\n            // Note: this expression returns true on ChromeOS, so additional detectors are required to avoid false-positives\n            n[c] && 'ontypechange' in n[c],\n            !('sinkId' in new Audio()),\n        ]) >= 2);\n    }\n    else if (isItGecko) {\n        return countTruthy(['onorientationchange' in w, 'orientation' in w, /android/i.test(n.appVersion)]) >= 2;\n    }\n    else {\n        // Only 2 browser engines are presented on Android.\n        // Actually, there is also Android 4.1 browser, but it's not worth detecting it at the moment.\n        return false;\n    }\n}\n/**\n * Checks whether the browser is Samsung Internet without using user-agent.\n * It doesn't check that the browser is based on Chromium, please use `isChromium` before using this function.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isSamsungInternet() {\n    // Checked in Samsung Internet 21, 25 and 27\n    var n = navigator;\n    var w = window;\n    var audioPrototype = Audio.prototype;\n    var visualViewport = w.visualViewport;\n    return (countTruthy([\n        'srLatency' in audioPrototype,\n        'srChannelCount' in audioPrototype,\n        'devicePosture' in n,\n        visualViewport && 'segments' in visualViewport,\n        'getTextInformation' in Image.prototype, // Not available in Samsung Internet 21\n    ]) >= 3);\n}\n\n/**\n * A deep description: https://fingerprint.com/blog/audio-fingerprinting/\n * Inspired by and based on https://github.com/cozylife/audio-fingerprint\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Audio signal is noised in private mode of Safari 17, so audio fingerprinting is skipped in Safari 17.\n */\nfunction getAudioFingerprint() {\n    if (doesBrowserPerformAntifingerprinting$1()) {\n        return -4 /* SpecialFingerprint.KnownForAntifingerprinting */;\n    }\n    return getUnstableAudioFingerprint();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableAudioFingerprint() {\n    var w = window;\n    var AudioContext = w.OfflineAudioContext || w.webkitOfflineAudioContext;\n    if (!AudioContext) {\n        return -2 /* SpecialFingerprint.NotSupported */;\n    }\n    // In some browsers, audio context always stays suspended unless the context is started in response to a user action\n    // (e.g. a click or a tap). It prevents audio fingerprint from being taken at an arbitrary moment of time.\n    // Such browsers are old and unpopular, so the audio fingerprinting is just skipped in them.\n    // See a similar case explanation at https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n    if (doesBrowserSuspendAudioContext()) {\n        return -1 /* SpecialFingerprint.KnownForSuspending */;\n    }\n    var hashFromIndex = 4500;\n    var hashToIndex = 5000;\n    var context = new AudioContext(1, hashToIndex, 44100);\n    var oscillator = context.createOscillator();\n    oscillator.type = 'triangle';\n    oscillator.frequency.value = 10000;\n    var compressor = context.createDynamicsCompressor();\n    compressor.threshold.value = -50;\n    compressor.knee.value = 40;\n    compressor.ratio.value = 12;\n    compressor.attack.value = 0;\n    compressor.release.value = 0.25;\n    oscillator.connect(compressor);\n    compressor.connect(context.destination);\n    oscillator.start(0);\n    var _a = startRenderingAudio(context), renderPromise = _a[0], finishRendering = _a[1];\n    // Suppresses the console error message in case when the fingerprint fails before requested\n    var fingerprintPromise = suppressUnhandledRejectionWarning(renderPromise.then(function (buffer) { return getHash(buffer.getChannelData(0).subarray(hashFromIndex)); }, function (error) {\n        if (error.name === \"timeout\" /* InnerErrorName.Timeout */ || error.name === \"suspended\" /* InnerErrorName.Suspended */) {\n            return -3 /* SpecialFingerprint.Timeout */;\n        }\n        throw error;\n    }));\n    return function () {\n        finishRendering();\n        return fingerprintPromise;\n    };\n}\n/**\n * Checks if the current browser is known for always suspending audio context\n */\nfunction doesBrowserSuspendAudioContext() {\n    // Mobile Safari 11 and older\n    return isWebKit() && !isDesktopWebKit() && !isWebKit606OrNewer();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting$1() {\n    return (\n    // Safari ≥17\n    (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) ||\n        // Samsung Internet ≥26\n        (isChromium() && isSamsungInternet() && isChromium122OrNewer()));\n}\n/**\n * Starts rendering the audio context.\n * When the returned function is called, the render process starts finishing.\n */\nfunction startRenderingAudio(context) {\n    var renderTryMaxCount = 3;\n    var renderRetryDelay = 500;\n    var runningMaxAwaitTime = 500;\n    var runningSufficientTime = 5000;\n    var finalize = function () { return undefined; };\n    var resultPromise = new Promise(function (resolve, reject) {\n        var isFinalized = false;\n        var renderTryCount = 0;\n        var startedRunningAt = 0;\n        context.oncomplete = function (event) { return resolve(event.renderedBuffer); };\n        var startRunningTimeout = function () {\n            setTimeout(function () { return reject(makeInnerError(\"timeout\" /* InnerErrorName.Timeout */)); }, Math.min(runningMaxAwaitTime, startedRunningAt + runningSufficientTime - Date.now()));\n        };\n        var tryRender = function () {\n            try {\n                var renderingPromise = context.startRendering();\n                // `context.startRendering` has two APIs: Promise and callback, we check that it's really a promise just in case\n                if (isPromise(renderingPromise)) {\n                    // Suppresses all unhandled rejections in case of scheduled redundant retries after successful rendering\n                    suppressUnhandledRejectionWarning(renderingPromise);\n                }\n                switch (context.state) {\n                    case 'running':\n                        startedRunningAt = Date.now();\n                        if (isFinalized) {\n                            startRunningTimeout();\n                        }\n                        break;\n                    // Sometimes the audio context doesn't start after calling `startRendering` (in addition to the cases where\n                    // audio context doesn't start at all). A known case is starting an audio context when the browser tab is in\n                    // background on iPhone. Retries usually help in this case.\n                    case 'suspended':\n                        // The audio context can reject starting until the tab is in foreground. Long fingerprint duration\n                        // in background isn't a problem, therefore the retry attempts don't count in background. It can lead to\n                        // a situation when a fingerprint takes very long time and finishes successfully. FYI, the audio context\n                        // can be suspended when `document.hidden === false` and start running after a retry.\n                        if (!document.hidden) {\n                            renderTryCount++;\n                        }\n                        if (isFinalized && renderTryCount >= renderTryMaxCount) {\n                            reject(makeInnerError(\"suspended\" /* InnerErrorName.Suspended */));\n                        }\n                        else {\n                            setTimeout(tryRender, renderRetryDelay);\n                        }\n                        break;\n                }\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        tryRender();\n        finalize = function () {\n            if (!isFinalized) {\n                isFinalized = true;\n                if (startedRunningAt > 0) {\n                    startRunningTimeout();\n                }\n            }\n        };\n    });\n    return [resultPromise, finalize];\n}\nfunction getHash(signal) {\n    var hash = 0;\n    for (var i = 0; i < signal.length; ++i) {\n        hash += Math.abs(signal[i]);\n    }\n    return hash;\n}\nfunction makeInnerError(name) {\n    var error = new Error(name);\n    error.name = name;\n    return error;\n}\n\n/**\n * Creates and keeps an invisible iframe while the given function runs.\n * The given function is called when the iframe is loaded and has a body.\n * The iframe allows to measure DOM sizes inside itself.\n *\n * Notice: passing an initial HTML code doesn't work in IE.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction withIframe(action, initialHtml, domPollInterval) {\n    var _a, _b, _c;\n    if (domPollInterval === void 0) { domPollInterval = 50; }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var d, iframe;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    d = document;\n                    _d.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 2:\n                    _d.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    iframe = d.createElement('iframe');\n                    _d.label = 4;\n                case 4:\n                    _d.trys.push([4, , 10, 11]);\n                    return [4 /*yield*/, new Promise(function (_resolve, _reject) {\n                            var isComplete = false;\n                            var resolve = function () {\n                                isComplete = true;\n                                _resolve();\n                            };\n                            var reject = function (error) {\n                                isComplete = true;\n                                _reject(error);\n                            };\n                            iframe.onload = resolve;\n                            iframe.onerror = reject;\n                            var style = iframe.style;\n                            style.setProperty('display', 'block', 'important'); // Required for browsers to calculate the layout\n                            style.position = 'absolute';\n                            style.top = '0';\n                            style.left = '0';\n                            style.visibility = 'hidden';\n                            if (initialHtml && 'srcdoc' in iframe) {\n                                iframe.srcdoc = initialHtml;\n                            }\n                            else {\n                                iframe.src = 'about:blank';\n                            }\n                            d.body.appendChild(iframe);\n                            // WebKit in WeChat doesn't fire the iframe's `onload` for some reason.\n                            // This code checks for the loading state manually.\n                            // See https://github.com/fingerprintjs/fingerprintjs/issues/645\n                            var checkReadyState = function () {\n                                var _a, _b;\n                                // The ready state may never become 'complete' in Firefox despite the 'load' event being fired.\n                                // So an infinite setTimeout loop can happen without this check.\n                                // See https://github.com/fingerprintjs/fingerprintjs/pull/716#issuecomment-986898796\n                                if (isComplete) {\n                                    return;\n                                }\n                                // Make sure iframe.contentWindow and iframe.contentWindow.document are both loaded\n                                // The contentWindow.document can miss in JSDOM (https://github.com/jsdom/jsdom).\n                                if (((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.readyState) === 'complete') {\n                                    resolve();\n                                }\n                                else {\n                                    setTimeout(checkReadyState, 10);\n                                }\n                            };\n                            checkReadyState();\n                        })];\n                case 5:\n                    _d.sent();\n                    _d.label = 6;\n                case 6:\n                    if (!!((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.body)) return [3 /*break*/, 8];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 7:\n                    _d.sent();\n                    return [3 /*break*/, 6];\n                case 8: return [4 /*yield*/, action(iframe, iframe.contentWindow)];\n                case 9: return [2 /*return*/, _d.sent()];\n                case 10:\n                    (_c = iframe.parentNode) === null || _c === void 0 ? void 0 : _c.removeChild(iframe);\n                    return [7 /*endfinally*/];\n                case 11: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Creates a DOM element that matches the given selector.\n * Only single element selector are supported (without operators like space, +, >, etc).\n */\nfunction selectorToElement(selector) {\n    var _a = parseSimpleCssSelector(selector), tag = _a[0], attributes = _a[1];\n    var element = document.createElement(tag !== null && tag !== void 0 ? tag : 'div');\n    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {\n        var name_1 = _b[_i];\n        var value = attributes[name_1].join(' ');\n        // Changing the `style` attribute can cause a CSP error, therefore we change the `style.cssText` property.\n        // https://github.com/fingerprintjs/fingerprintjs/issues/733\n        if (name_1 === 'style') {\n            addStyleString(element.style, value);\n        }\n        else {\n            element.setAttribute(name_1, value);\n        }\n    }\n    return element;\n}\n/**\n * Adds CSS styles from a string in such a way that doesn't trigger a CSP warning (unsafe-inline or unsafe-eval)\n */\nfunction addStyleString(style, source) {\n    // We don't use `style.cssText` because browsers must block it when no `unsafe-eval` CSP is presented: https://csplite.com/csp145/#w3c_note\n    // Even though the browsers ignore this standard, we don't use `cssText` just in case.\n    for (var _i = 0, _a = source.split(';'); _i < _a.length; _i++) {\n        var property = _a[_i];\n        var match = /^\\s*([\\w-]+)\\s*:\\s*(.+?)(\\s*!([\\w-]+))?\\s*$/.exec(property);\n        if (match) {\n            var name_2 = match[1], value = match[2], priority = match[4];\n            style.setProperty(name_2, value, priority || ''); // The last argument can't be undefined in IE11\n        }\n    }\n}\n/**\n * Returns true if the code runs in an iframe, and any parent page's origin doesn't match the current origin\n */\nfunction isAnyParentCrossOrigin() {\n    var currentWindow = window;\n    for (;;) {\n        var parentWindow = currentWindow.parent;\n        if (!parentWindow || parentWindow === currentWindow) {\n            return false; // The top page is reached\n        }\n        try {\n            if (parentWindow.location.origin !== currentWindow.location.origin) {\n                return true;\n            }\n        }\n        catch (error) {\n            // The error is thrown when `origin` is accessed on `parentWindow.location` when the parent is cross-origin\n            if (error instanceof Error && error.name === 'SecurityError') {\n                return true;\n            }\n            throw error;\n        }\n        currentWindow = parentWindow;\n    }\n}\n\n// We use m or w because these two characters take up the maximum width.\n// And we use a LLi so that the same matching fonts can get separated.\nvar testString = 'mmMwWLliI0O&1';\n// We test using 48px font size, we may use any size. I guess larger the better.\nvar textSize = '48px';\n// A font will be compared against all the three default fonts.\n// And if for any default fonts it doesn't match, then that font is available.\nvar baseFonts = ['monospace', 'sans-serif', 'serif'];\nvar fontList = [\n    // This is android-specific font from \"Roboto\" family\n    'sans-serif-thin',\n    'ARNO PRO',\n    'Agency FB',\n    'Arabic Typesetting',\n    'Arial Unicode MS',\n    'AvantGarde Bk BT',\n    'BankGothic Md BT',\n    'Batang',\n    'Bitstream Vera Sans Mono',\n    'Calibri',\n    'Century',\n    'Century Gothic',\n    'Clarendon',\n    'EUROSTILE',\n    'Franklin Gothic',\n    'Futura Bk BT',\n    'Futura Md BT',\n    'GOTHAM',\n    'Gill Sans',\n    'HELV',\n    'Haettenschweiler',\n    'Helvetica Neue',\n    'Humanst521 BT',\n    'Leelawadee',\n    'Letter Gothic',\n    'Levenim MT',\n    'Lucida Bright',\n    'Lucida Sans',\n    'Menlo',\n    'MS Mincho',\n    'MS Outlook',\n    'MS Reference Specialty',\n    'MS UI Gothic',\n    'MT Extra',\n    'MYRIAD PRO',\n    'Marlett',\n    'Meiryo UI',\n    'Microsoft Uighur',\n    'Minion Pro',\n    'Monotype Corsiva',\n    'PMingLiU',\n    'Pristina',\n    'SCRIPTINA',\n    'Segoe UI Light',\n    'Serifa',\n    'SimHei',\n    'Small Fonts',\n    'Staccato222 BT',\n    'TRAJAN PRO',\n    'Univers CE 55 Medium',\n    'Vrinda',\n    'ZWAdobeF',\n];\n// kudos to http://www.lalit.org/lab/javascript-css-font-detect/\nfunction getFonts() {\n    var _this = this;\n    // Running the script in an iframe makes it not affect the page look and not be affected by the page CSS. See:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/592\n    // https://github.com/fingerprintjs/fingerprintjs/issues/628\n    return withIframe(function (_, _a) {\n        var document = _a.document;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(_this, void 0, void 0, function () {\n            var holder, spansContainer, defaultWidth, defaultHeight, createSpan, createSpanWithFonts, initializeBaseFontsSpans, initializeFontsSpans, isFontAvailable, baseFontsSpans, fontsSpans, index;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_b) {\n                holder = document.body;\n                holder.style.fontSize = textSize;\n                spansContainer = document.createElement('div');\n                spansContainer.style.setProperty('visibility', 'hidden', 'important');\n                defaultWidth = {};\n                defaultHeight = {};\n                createSpan = function (fontFamily) {\n                    var span = document.createElement('span');\n                    var style = span.style;\n                    style.position = 'absolute';\n                    style.top = '0';\n                    style.left = '0';\n                    style.fontFamily = fontFamily;\n                    span.textContent = testString;\n                    spansContainer.appendChild(span);\n                    return span;\n                };\n                createSpanWithFonts = function (fontToDetect, baseFont) {\n                    return createSpan(\"'\".concat(fontToDetect, \"',\").concat(baseFont));\n                };\n                initializeBaseFontsSpans = function () {\n                    return baseFonts.map(createSpan);\n                };\n                initializeFontsSpans = function () {\n                    // Stores {fontName : [spans for that font]}\n                    var spans = {};\n                    var _loop_1 = function (font) {\n                        spans[font] = baseFonts.map(function (baseFont) { return createSpanWithFonts(font, baseFont); });\n                    };\n                    for (var _i = 0, fontList_1 = fontList; _i < fontList_1.length; _i++) {\n                        var font = fontList_1[_i];\n                        _loop_1(font);\n                    }\n                    return spans;\n                };\n                isFontAvailable = function (fontSpans) {\n                    return baseFonts.some(function (baseFont, baseFontIndex) {\n                        return fontSpans[baseFontIndex].offsetWidth !== defaultWidth[baseFont] ||\n                            fontSpans[baseFontIndex].offsetHeight !== defaultHeight[baseFont];\n                    });\n                };\n                baseFontsSpans = initializeBaseFontsSpans();\n                fontsSpans = initializeFontsSpans();\n                // add all the spans to the DOM\n                holder.appendChild(spansContainer);\n                // get the default width for the three base fonts\n                for (index = 0; index < baseFonts.length; index++) {\n                    defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth; // width for the default font\n                    defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight; // height for the default font\n                }\n                // check available fonts\n                return [2 /*return*/, fontList.filter(function (font) { return isFontAvailable(fontsSpans[font]); })];\n            });\n        });\n    });\n}\n\nfunction getPlugins() {\n    var rawPlugins = navigator.plugins;\n    if (!rawPlugins) {\n        return undefined;\n    }\n    var plugins = [];\n    // Safari 10 doesn't support iterating navigator.plugins with for...of\n    for (var i = 0; i < rawPlugins.length; ++i) {\n        var plugin = rawPlugins[i];\n        if (!plugin) {\n            continue;\n        }\n        var mimeTypes = [];\n        for (var j = 0; j < plugin.length; ++j) {\n            var mimeType = plugin[j];\n            mimeTypes.push({\n                type: mimeType.type,\n                suffixes: mimeType.suffixes,\n            });\n        }\n        plugins.push({\n            name: plugin.name,\n            description: plugin.description,\n            mimeTypes: mimeTypes,\n        });\n    }\n    return plugins;\n}\n\n/**\n * @see https://www.browserleaks.com/canvas#how-does-it-work\n *\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * Canvas image is noised in private mode of Safari 17, so image rendering is skipped in Safari 17.\n */\nfunction getCanvasFingerprint() {\n    return getUnstableCanvasFingerprint(doesBrowserPerformAntifingerprinting());\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableCanvasFingerprint(skipImages) {\n    var _a;\n    var winding = false;\n    var geometry;\n    var text;\n    var _b = makeCanvasContext(), canvas = _b[0], context = _b[1];\n    if (!isSupported(canvas, context)) {\n        geometry = text = \"unsupported\" /* ImageStatus.Unsupported */;\n    }\n    else {\n        winding = doesSupportWinding(context);\n        if (skipImages) {\n            geometry = text = \"skipped\" /* ImageStatus.Skipped */;\n        }\n        else {\n            _a = renderImages(canvas, context), geometry = _a[0], text = _a[1];\n        }\n    }\n    return { winding: winding, geometry: geometry, text: text };\n}\nfunction makeCanvasContext() {\n    var canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    return [canvas, canvas.getContext('2d')];\n}\nfunction isSupported(canvas, context) {\n    return !!(context && canvas.toDataURL);\n}\nfunction doesSupportWinding(context) {\n    // https://web.archive.org/web/20170825024655/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    context.rect(0, 0, 10, 10);\n    context.rect(2, 2, 6, 6);\n    return !context.isPointInPath(5, 5, 'evenodd');\n}\nfunction renderImages(canvas, context) {\n    renderTextImage(canvas, context);\n    var textImage1 = canvasToString(canvas);\n    var textImage2 = canvasToString(canvas); // It's slightly faster to double-encode the text image\n    // Some browsers add a noise to the canvas: https://github.com/fingerprintjs/fingerprintjs/issues/791\n    // The canvas is excluded from the fingerprint in this case\n    if (textImage1 !== textImage2) {\n        return [\"unstable\" /* ImageStatus.Unstable */, \"unstable\" /* ImageStatus.Unstable */];\n    }\n    // Text is unstable:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/583\n    // https://github.com/fingerprintjs/fingerprintjs/issues/103\n    // Therefore it's extracted into a separate image.\n    renderGeometryImage(canvas, context);\n    var geometryImage = canvasToString(canvas);\n    return [geometryImage, textImage1];\n}\nfunction renderTextImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 240;\n    canvas.height = 60;\n    context.textBaseline = 'alphabetic';\n    context.fillStyle = '#f60';\n    context.fillRect(100, 1, 62, 20);\n    context.fillStyle = '#069';\n    // It's important to use explicit built-in fonts in order to exclude the affect of font preferences\n    // (there is a separate entropy source for them).\n    context.font = '11pt \"Times New Roman\"';\n    // The choice of emojis has a gigantic impact on rendering performance (especially in FF).\n    // Some newer emojis cause it to slow down 50-200 times.\n    // There must be no text to the right of the emoji, see https://github.com/fingerprintjs/fingerprintjs/issues/574\n    // A bare emoji shouldn't be used because the canvas will change depending on the script encoding:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    // Escape sequence shouldn't be used too because Terser will turn it into a bare unicode.\n    var printedText = \"Cwm fjordbank gly \".concat(String.fromCharCode(55357, 56835) /* 😃 */);\n    context.fillText(printedText, 2, 15);\n    context.fillStyle = 'rgba(102, 204, 0, 0.2)';\n    context.font = '18pt Arial';\n    context.fillText(printedText, 4, 45);\n}\nfunction renderGeometryImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 122;\n    canvas.height = 110;\n    // Canvas blending\n    // https://web.archive.org/web/**************/http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    context.globalCompositeOperation = 'multiply';\n    for (var _i = 0, _a = [\n        ['#f2f', 40, 40],\n        ['#2ff', 80, 40],\n        ['#ff2', 60, 80],\n    ]; _i < _a.length; _i++) {\n        var _b = _a[_i], color = _b[0], x = _b[1], y = _b[2];\n        context.fillStyle = color;\n        context.beginPath();\n        context.arc(x, y, 40, 0, Math.PI * 2, true);\n        context.closePath();\n        context.fill();\n    }\n    // Canvas winding\n    // https://web.archive.org/web/20130913061632/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    context.fillStyle = '#f9c';\n    context.arc(60, 60, 60, 0, Math.PI * 2, true);\n    context.arc(60, 60, 20, 0, Math.PI * 2, true);\n    context.fill('evenodd');\n}\nfunction canvasToString(canvas) {\n    return canvas.toDataURL();\n}\n/**\n * Checks if the current browser is known for applying anti-fingerprinting measures in all or some critical modes\n */\nfunction doesBrowserPerformAntifingerprinting() {\n    // Safari 17\n    return isWebKit() && isWebKit616OrNewer() && isSafariWebKit();\n}\n\n/**\n * This is a crude and primitive touch screen detection. It's not possible to currently reliably detect the availability\n * of a touch screen with a JS, without actually subscribing to a touch event.\n *\n * @see http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n * @see https://github.com/Modernizr/Modernizr/issues/548\n */\nfunction getTouchSupport() {\n    var n = navigator;\n    var maxTouchPoints = 0;\n    var touchEvent;\n    if (n.maxTouchPoints !== undefined) {\n        maxTouchPoints = toInt(n.maxTouchPoints);\n    }\n    else if (n.msMaxTouchPoints !== undefined) {\n        maxTouchPoints = n.msMaxTouchPoints;\n    }\n    try {\n        document.createEvent('TouchEvent');\n        touchEvent = true;\n    }\n    catch (_a) {\n        touchEvent = false;\n    }\n    var touchStart = 'ontouchstart' in window;\n    return {\n        maxTouchPoints: maxTouchPoints,\n        touchEvent: touchEvent,\n        touchStart: touchStart,\n    };\n}\n\nfunction getOsCpu() {\n    return navigator.oscpu;\n}\n\nfunction getLanguages() {\n    var n = navigator;\n    var result = [];\n    var language = n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;\n    if (language !== undefined) {\n        result.push([language]);\n    }\n    if (Array.isArray(n.languages)) {\n        // Starting from Chromium 86, there is only a single value in `navigator.language` in Incognito mode:\n        // the value of `navigator.language`. Therefore the value is ignored in this browser.\n        if (!(isChromium() && isChromium86OrNewer())) {\n            result.push(n.languages);\n        }\n    }\n    else if (typeof n.languages === 'string') {\n        var languages = n.languages;\n        if (languages) {\n            result.push(languages.split(','));\n        }\n    }\n    return result;\n}\n\nfunction getColorDepth() {\n    return window.screen.colorDepth;\n}\n\nfunction getDeviceMemory() {\n    // `navigator.deviceMemory` is a string containing a number in some unidentified cases\n    return replaceNaN(toFloat(navigator.deviceMemory), undefined);\n}\n\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n * The window resolution is always the document size in private mode of Safari 17,\n * so the window resolution is not used in Safari 17.\n */\nfunction getScreenResolution() {\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return undefined;\n    }\n    return getUnstableScreenResolution();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenResolution() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    // Some browsers even return  screen resolution as not numbers.\n    var parseDimension = function (value) { return replaceNaN(toInt(value), null); };\n    var dimensions = [parseDimension(s.width), parseDimension(s.height)];\n    dimensions.sort().reverse();\n    return dimensions;\n}\n\nvar screenFrameCheckInterval = 2500;\nvar roundingPrecision = 10;\n// The type is readonly to protect from unwanted mutations\nvar screenFrameBackup;\nvar screenFrameSizeTimeoutId;\n/**\n * Starts watching the screen frame size. When a non-zero size appears, the size is saved and the watch is stopped.\n * Later, when `getScreenFrame` runs, it will return the saved non-zero size if the current size is null.\n *\n * This trick is required to mitigate the fact that the screen frame turns null in some cases.\n * See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n */\nfunction watchScreenFrame() {\n    if (screenFrameSizeTimeoutId !== undefined) {\n        return;\n    }\n    var checkScreenFrame = function () {\n        var frameSize = getCurrentScreenFrame();\n        if (isFrameSizeNull(frameSize)) {\n            screenFrameSizeTimeoutId = setTimeout(checkScreenFrame, screenFrameCheckInterval);\n        }\n        else {\n            screenFrameBackup = frameSize;\n            screenFrameSizeTimeoutId = undefined;\n        }\n    };\n    checkScreenFrame();\n}\n/**\n * A version of the entropy source without stabilization.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getUnstableScreenFrame() {\n    var _this = this;\n    watchScreenFrame();\n    return function () { return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(_this, void 0, void 0, function () {\n        var frameSize;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    frameSize = getCurrentScreenFrame();\n                    if (!isFrameSizeNull(frameSize)) return [3 /*break*/, 2];\n                    if (screenFrameBackup) {\n                        return [2 /*return*/, (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)([], screenFrameBackup, true)];\n                    }\n                    if (!getFullscreenElement()) return [3 /*break*/, 2];\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    return [4 /*yield*/, exitFullscreen()];\n                case 1:\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    _a.sent();\n                    frameSize = getCurrentScreenFrame();\n                    _a.label = 2;\n                case 2:\n                    if (!isFrameSizeNull(frameSize)) {\n                        screenFrameBackup = frameSize;\n                    }\n                    return [2 /*return*/, frameSize];\n            }\n        });\n    }); };\n}\n/**\n * A version of the entropy source with stabilization to make it suitable for static fingerprinting.\n *\n * Sometimes the available screen resolution changes a bit, e.g. 1900x1440 → 1900x1439. A possible reason: macOS Dock\n * shrinks to fit more icons when there is too little space. The rounding is used to mitigate the difference.\n *\n * The frame width is always 0 in private mode of Safari 17, so the frame is not used in Safari 17.\n */\nfunction getScreenFrame() {\n    var _this = this;\n    if (isWebKit() && isWebKit616OrNewer() && isSafariWebKit()) {\n        return function () { return Promise.resolve(undefined); };\n    }\n    var screenFrameGetter = getUnstableScreenFrame();\n    return function () { return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(_this, void 0, void 0, function () {\n        var frameSize, processSize;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, screenFrameGetter()];\n                case 1:\n                    frameSize = _a.sent();\n                    processSize = function (sideSize) { return (sideSize === null ? null : round(sideSize, roundingPrecision)); };\n                    // It might look like I don't know about `for` and `map`.\n                    // In fact, such code is used to avoid TypeScript issues without using `as`.\n                    return [2 /*return*/, [processSize(frameSize[0]), processSize(frameSize[1]), processSize(frameSize[2]), processSize(frameSize[3])]];\n            }\n        });\n    }); };\n}\nfunction getCurrentScreenFrame() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    //\n    // Some browsers (IE, Edge ≤18) don't provide `screen.availLeft` and `screen.availTop`. The property values are\n    // replaced with 0 in such cases to not lose the entropy from `screen.availWidth` and `screen.availHeight`.\n    return [\n        replaceNaN(toFloat(s.availTop), null),\n        replaceNaN(toFloat(s.width) - toFloat(s.availWidth) - replaceNaN(toFloat(s.availLeft), 0), null),\n        replaceNaN(toFloat(s.height) - toFloat(s.availHeight) - replaceNaN(toFloat(s.availTop), 0), null),\n        replaceNaN(toFloat(s.availLeft), null),\n    ];\n}\nfunction isFrameSizeNull(frameSize) {\n    for (var i = 0; i < 4; ++i) {\n        if (frameSize[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getHardwareConcurrency() {\n    // sometimes hardware concurrency is a string\n    return replaceNaN(toInt(navigator.hardwareConcurrency), undefined);\n}\n\nfunction getTimezone() {\n    var _a;\n    var DateTimeFormat = (_a = window.Intl) === null || _a === void 0 ? void 0 : _a.DateTimeFormat;\n    if (DateTimeFormat) {\n        var timezone = new DateTimeFormat().resolvedOptions().timeZone;\n        if (timezone) {\n            return timezone;\n        }\n    }\n    // For browsers that don't support timezone names\n    // The minus is intentional because the JS offset is opposite to the real offset\n    var offset = -getTimezoneOffset();\n    return \"UTC\".concat(offset >= 0 ? '+' : '').concat(offset);\n}\nfunction getTimezoneOffset() {\n    var currentYear = new Date().getFullYear();\n    // The timezone offset may change over time due to daylight saving time (DST) shifts.\n    // The non-DST timezone offset is used as the result timezone offset.\n    // Since the DST season differs in the northern and the southern hemispheres,\n    // both January and July timezones offsets are considered.\n    return Math.max(\n    // `getTimezoneOffset` returns a number as a string in some unidentified cases\n    toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()), toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()));\n}\n\nfunction getSessionStorage() {\n    try {\n        return !!window.sessionStorage;\n    }\n    catch (error) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\n// https://bugzilla.mozilla.org/show_bug.cgi?id=781447\nfunction getLocalStorage() {\n    try {\n        return !!window.localStorage;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getIndexedDB() {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // visitor identifier in normal and private modes.\n    if (isTrident() || isEdgeHTML()) {\n        return undefined;\n    }\n    try {\n        return !!window.indexedDB;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getOpenDatabase() {\n    return !!window.openDatabase;\n}\n\nfunction getCpuClass() {\n    return navigator.cpuClass;\n}\n\nfunction getPlatform() {\n    // Android Chrome 86 and 87 and Android Firefox 80 and 84 don't mock the platform value when desktop mode is requested\n    var platform = navigator.platform;\n    // iOS mocks the platform value when desktop version is requested: https://github.com/fingerprintjs/fingerprintjs/issues/514\n    // iPad uses desktop mode by default since iOS 13\n    // The value is 'MacIntel' on M1 Macs\n    // The value is 'iPhone' on iPod Touch\n    if (platform === 'MacIntel') {\n        if (isWebKit() && !isDesktopWebKit()) {\n            return isIPad() ? 'iPad' : 'iPhone';\n        }\n    }\n    return platform;\n}\n\nfunction getVendor() {\n    return navigator.vendor || '';\n}\n\n/**\n * Checks for browser-specific (not engine specific) global variables to tell browsers with the same engine apart.\n * Only somewhat popular browsers are considered.\n */\nfunction getVendorFlavors() {\n    var flavors = [];\n    for (var _i = 0, _a = [\n        // Blink and some browsers on iOS\n        'chrome',\n        // Safari on macOS\n        'safari',\n        // Chrome on iOS (checked in 85 on 13 and 87 on 14)\n        '__crWeb',\n        '__gCrWeb',\n        // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)\n        'yandex',\n        // Yandex Browser on iOS (checked in 21.2 on 14)\n        '__yb',\n        '__ybro',\n        // Firefox on iOS (checked in 32 on 14)\n        '__firefox__',\n        // Edge on iOS (checked in 46 on 14)\n        '__edgeTrackingPreventionStatistics',\n        'webkit',\n        // Opera Touch on iOS (checked in 2.6 on 14)\n        'oprt',\n        // Samsung Internet on Android (checked in 11.1)\n        'samsungAr',\n        // UC Browser on Android (checked in 12.10 and 13.0)\n        'ucweb',\n        'UCShellJava',\n        // Puffin on Android (checked in 9.0)\n        'puffinDevice',\n        // UC on iOS and Opera on Android have no specific global variables\n        // Edge for Android isn't checked\n    ]; _i < _a.length; _i++) {\n        var key = _a[_i];\n        var value = window[key];\n        if (value && typeof value === 'object') {\n            flavors.push(key);\n        }\n    }\n    return flavors.sort();\n}\n\n/**\n * navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n * cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past with\n * site-specific exceptions. Don't rely on it.\n *\n * @see https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js Taken from here\n */\nfunction areCookiesEnabled() {\n    var d = document;\n    // Taken from here: https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js\n    // navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n    // cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past\n    // with site-specific exceptions. Don't rely on it.\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    try {\n        // Create cookie\n        d.cookie = 'cookietest=1; SameSite=Strict;';\n        var result = d.cookie.indexOf('cookietest=') !== -1;\n        // Delete cookie\n        d.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n        return result;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\n/**\n * Only single element selector are supported (no operators like space, +, >, etc).\n * `embed` and `position: fixed;` will be considered as blocked anyway because it always has no offsetParent.\n * Avoid `iframe` and anything with `[src=]` because they produce excess HTTP requests.\n *\n * The \"inappropriate\" selectors are obfuscated. See https://github.com/fingerprintjs/fingerprintjs/issues/734.\n * A function is used instead of a plain object to help tree-shaking.\n *\n * The function code is generated automatically. See docs/content_blockers.md to learn how to make the list.\n */\nfunction getFilters() {\n    var fromB64 = atob; // Just for better minification\n    return {\n        abpIndo: [\n            '#Iklan-Melayang',\n            '#Kolom-Iklan-728',\n            '#SidebarIklan-wrapper',\n            '[title=\"ALIENBOLA\" i]',\n            fromB64('I0JveC1CYW5uZXItYWRz'),\n        ],\n        abpvn: ['.quangcao', '#mobileCatfish', fromB64('LmNsb3NlLWFkcw=='), '[id^=\"bn_bottom_fixed_\"]', '#pmadv'],\n        adBlockFinland: [\n            '.mainostila',\n            fromB64('LnNwb25zb3JpdA=='),\n            '.ylamainos',\n            fromB64('YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd'),\n        ],\n        adBlockPersian: [\n            '#navbar_notice_50',\n            '.kadr',\n            'TABLE[width=\"140px\"]',\n            '#divAgahi',\n            fromB64('YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd'),\n        ],\n        adBlockWarningRemoval: [\n            '#adblock-honeypot',\n            '.adblocker-root',\n            '.wp_adblock_detect',\n            fromB64('LmhlYWRlci1ibG9ja2VkLWFk'),\n            fromB64('I2FkX2Jsb2NrZXI='),\n        ],\n        adGuardAnnoyances: [\n            '.hs-sosyal',\n            '#cookieconsentdiv',\n            'div[class^=\"app_gdpr\"]',\n            '.as-oil',\n            '[data-cypress=\"soft-push-notification-modal\"]',\n        ],\n        adGuardBase: [\n            '.BetterJsPopOverlay',\n            fromB64('I2FkXzMwMFgyNTA='),\n            fromB64('I2Jhbm5lcmZsb2F0MjI='),\n            fromB64('I2NhbXBhaWduLWJhbm5lcg=='),\n            fromB64('I0FkLUNvbnRlbnQ='),\n        ],\n        adGuardChinese: [\n            fromB64('LlppX2FkX2FfSA=='),\n            fromB64('YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd'),\n            '#widget-quan',\n            fromB64('YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd'),\n            fromB64('YVtocmVmKj0iLjE5NTZobC5jb20vIl0='),\n        ],\n        adGuardFrench: [\n            '#pavePub',\n            fromB64('LmFkLWRlc2t0b3AtcmVjdGFuZ2xl'),\n            '.mobile_adhesion',\n            '.widgetadv',\n            fromB64('LmFkc19iYW4='),\n        ],\n        adGuardGerman: ['aside[data-portal-id=\"leaderboard\"]'],\n        adGuardJapanese: [\n            '#kauli_yad_1',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0='),\n            fromB64('Ll9wb3BJbl9pbmZpbml0ZV9hZA=='),\n            fromB64('LmFkZ29vZ2xl'),\n            fromB64('Ll9faXNib29zdFJldHVybkFk'),\n        ],\n        adGuardMobile: [\n            fromB64('YW1wLWF1dG8tYWRz'),\n            fromB64('LmFtcF9hZA=='),\n            'amp-embed[type=\"24smi\"]',\n            '#mgid_iframe1',\n            fromB64('I2FkX2ludmlld19hcmVh'),\n        ],\n        adGuardRussian: [\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0='),\n            fromB64('LnJlY2xhbWE='),\n            'div[id^=\"smi2adblock\"]',\n            fromB64('ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd'),\n            '#psyduckpockeball',\n        ],\n        adGuardSocial: [\n            fromB64('YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0='),\n            fromB64('YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0='),\n            '.etsy-tweet',\n            '#inlineShare',\n            '.popup-social',\n        ],\n        adGuardSpanishPortuguese: ['#barraPublicidade', '#Publicidade', '#publiEspecial', '#queTooltip', '.cnt-publi'],\n        adGuardTrackingProtection: [\n            '#qoo-counter',\n            fromB64('YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=='),\n            '#top100counter',\n        ],\n        adGuardTurkish: [\n            '#backkapat',\n            fromB64('I3Jla2xhbWk='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ=='),\n        ],\n        bulgarian: [fromB64('dGQjZnJlZW5ldF90YWJsZV9hZHM='), '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],\n        easyList: [\n            '.yb-floorad',\n            fromB64('LndpZGdldF9wb19hZHNfd2lkZ2V0'),\n            fromB64('LnRyYWZmaWNqdW5reS1hZA=='),\n            '.textad_headline',\n            fromB64('LnNwb25zb3JlZC10ZXh0LWxpbmtz'),\n        ],\n        easyListChina: [\n            fromB64('LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=='),\n            fromB64('LmZyb250cGFnZUFkdk0='),\n            '#taotaole',\n            '#aafoot.top_box',\n            '.cfa_popup',\n        ],\n        easyListCookie: [\n            '.ezmob-footer',\n            '.cc-CookieWarning',\n            '[data-cookie-number]',\n            fromB64('LmF3LWNvb2tpZS1iYW5uZXI='),\n            '.sygnal24-gdpr-modal-wrap',\n        ],\n        easyListCzechSlovak: [\n            '#onlajny-stickers',\n            fromB64('I3Jla2xhbW5pLWJveA=='),\n            fromB64('LnJla2xhbWEtbWVnYWJvYXJk'),\n            '.sklik',\n            fromB64('W2lkXj0ic2tsaWtSZWtsYW1hIl0='),\n        ],\n        easyListDutch: [\n            fromB64('I2FkdmVydGVudGll'),\n            fromB64('I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=='),\n            '.adstekst',\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0='),\n            '#semilo-lrectangle',\n        ],\n        easyListGermany: [\n            '#SSpotIMPopSlider',\n            fromB64('LnNwb25zb3JsaW5rZ3J1ZW4='),\n            fromB64('I3dlcmJ1bmdza3k='),\n            fromB64('I3Jla2xhbWUtcmVjaHRzLW1pdHRl'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0='),\n        ],\n        easyListItaly: [\n            fromB64('LmJveF9hZHZfYW5udW5jaQ=='),\n            '.sb-box-pubbliredazionale',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ=='),\n        ],\n        easyListLithuania: [\n            fromB64('LnJla2xhbW9zX3RhcnBhcw=='),\n            fromB64('LnJla2xhbW9zX251b3JvZG9z'),\n            fromB64('aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd'),\n            fromB64('aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd'),\n            fromB64('aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd'),\n        ],\n        estonian: [fromB64('QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==')],\n        fanboyAnnoyances: ['#ac-lre-player', '.navigate-to-top', '#subscribe_popup', '.newsletter_holder', '#back-top'],\n        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],\n        fanboyEnhancedTrackers: [\n            '.open.pushModal',\n            '#issuem-leaky-paywall-articles-zero-remaining-nag',\n            '#sovrn_container',\n            'div[class$=\"-hide\"][zoompage-fontsize][style=\"display: block;\"]',\n            '.BlockNag__Card',\n        ],\n        fanboySocial: ['#FollowUs', '#meteored_share', '#social_follow', '.article-sharer', '.community__social-desc'],\n        frellwitSwedish: [\n            fromB64('YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=='),\n            fromB64('YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=='),\n            'article.category-samarbete',\n            fromB64('ZGl2LmhvbGlkQWRz'),\n            'ul.adsmodern',\n        ],\n        greekAdBlock: [\n            fromB64('QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd'),\n            fromB64('QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=='),\n            fromB64('QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd'),\n            'DIV.agores300',\n            'TABLE.advright',\n        ],\n        hungarian: [\n            '#cemp_doboz',\n            '.optimonk-iframe-container',\n            fromB64('LmFkX19tYWlu'),\n            fromB64('W2NsYXNzKj0iR29vZ2xlQWRzIl0='),\n            '#hirdetesek_box',\n        ],\n        iDontCareAboutCookies: [\n            '.alert-info[data-block-track*=\"CookieNotice\"]',\n            '.ModuleTemplateCookieIndicator',\n            '.o--cookies--container',\n            '#cookies-policy-sticky',\n            '#stickyCookieBar',\n        ],\n        icelandicAbp: [fromB64('QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==')],\n        latvian: [\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0O' +\n                'iA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0='),\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6I' +\n                'DMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ=='),\n        ],\n        listKr: [\n            fromB64('YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0='),\n            fromB64('I2xpdmVyZUFkV3JhcHBlcg=='),\n            fromB64('YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=='),\n            fromB64('aW5zLmZhc3R2aWV3LWFk'),\n            '.revenue_unit_item.dable',\n        ],\n        listeAr: [\n            fromB64('LmdlbWluaUxCMUFk'),\n            '.right-and-left-sponsers',\n            fromB64('YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=='),\n            fromB64('YVtocmVmKj0iYm9vcmFxLm9yZyJd'),\n            fromB64('YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd'),\n        ],\n        listeFr: [\n            fromB64('YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=='),\n            fromB64('I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=='),\n            fromB64('YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0='),\n            '.site-pub-interstitiel',\n            'div[id^=\"crt-\"][data-criteo-id]',\n        ],\n        officialPolish: [\n            '#ceneo-placeholder-ceneo-12',\n            fromB64('W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=='),\n            fromB64('ZGl2I3NrYXBpZWNfYWQ='),\n        ],\n        ro: [\n            fromB64('YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0='),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd'),\n            'a[href^=\"/url/\"]',\n        ],\n        ruAd: [\n            fromB64('YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd'),\n            fromB64('YVtocmVmKj0iLy91dGltZy5ydS8iXQ=='),\n            fromB64('YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0='),\n            '#pgeldiz',\n            '.yandex-rtb-block',\n        ],\n        thaiAds: [\n            'a[href*=macau-uta-popup]',\n            fromB64('I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=='),\n            fromB64('LmFkczMwMHM='),\n            '.bumq',\n            '.img-kosana',\n        ],\n        webAnnoyancesUltralist: [\n            '#mod-social-share-2',\n            '#social-tools',\n            fromB64('LmN0cGwtZnVsbGJhbm5lcg=='),\n            '.zergnet-recommend',\n            '.yt.btn-link.btn-md.btn',\n        ],\n    };\n}\n/**\n * The order of the returned array means nothing (it's always sorted alphabetically).\n *\n * Notice that the source is slightly unstable.\n * Safari provides a 2-taps way to disable all content blockers on a page temporarily.\n * Also content blockers can be disabled permanently for a domain, but it requires 4 taps.\n * So empty array shouldn't be treated as \"no blockers\", it should be treated as \"no signal\".\n * If you are a website owner, don't make your visitors want to disable content blockers.\n */\nfunction getDomBlockers(_a) {\n    var _b = _a === void 0 ? {} : _a, debug = _b.debug;\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var filters, filterNames, allSelectors, blockedSelectors, activeBlockers;\n        var _c;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (!isApplicable()) {\n                        return [2 /*return*/, undefined];\n                    }\n                    filters = getFilters();\n                    filterNames = Object.keys(filters);\n                    allSelectors = (_c = []).concat.apply(_c, filterNames.map(function (filterName) { return filters[filterName]; }));\n                    return [4 /*yield*/, getBlockedSelectors(allSelectors)];\n                case 1:\n                    blockedSelectors = _d.sent();\n                    if (debug) {\n                        printDebug(filters, blockedSelectors);\n                    }\n                    activeBlockers = filterNames.filter(function (filterName) {\n                        var selectors = filters[filterName];\n                        var blockedCount = countTruthy(selectors.map(function (selector) { return blockedSelectors[selector]; }));\n                        return blockedCount > selectors.length * 0.6;\n                    });\n                    activeBlockers.sort();\n                    return [2 /*return*/, activeBlockers];\n            }\n        });\n    });\n}\nfunction isApplicable() {\n    // Safari (desktop and mobile) and all Android browsers keep content blockers in both regular and private mode\n    return isWebKit() || isAndroid();\n}\nfunction getBlockedSelectors(selectors) {\n    var _a;\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var d, root, elements, blockedSelectors, i, element, holder, i;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    d = document;\n                    root = d.createElement('div');\n                    elements = new Array(selectors.length);\n                    blockedSelectors = {} // Set() isn't used just in case somebody need older browser support\n                    ;\n                    forceShow(root);\n                    // First create all elements that can be blocked. If the DOM steps below are done in a single cycle,\n                    // browser will alternate tree modification and layout reading, that is very slow.\n                    for (i = 0; i < selectors.length; ++i) {\n                        element = selectorToElement(selectors[i]);\n                        if (element.tagName === 'DIALOG') {\n                            element.show();\n                        }\n                        holder = d.createElement('div') // Protects from unwanted effects of `+` and `~` selectors of filters\n                        ;\n                        forceShow(holder);\n                        holder.appendChild(element);\n                        root.appendChild(holder);\n                        elements[i] = element;\n                    }\n                    _b.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(50)];\n                case 2:\n                    _b.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    d.body.appendChild(root);\n                    try {\n                        // Then check which of the elements are blocked\n                        for (i = 0; i < selectors.length; ++i) {\n                            if (!elements[i].offsetParent) {\n                                blockedSelectors[selectors[i]] = true;\n                            }\n                        }\n                    }\n                    finally {\n                        // Then remove the elements\n                        (_a = root.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(root);\n                    }\n                    return [2 /*return*/, blockedSelectors];\n            }\n        });\n    });\n}\nfunction forceShow(element) {\n    element.style.setProperty('visibility', 'hidden', 'important');\n    element.style.setProperty('display', 'block', 'important');\n}\nfunction printDebug(filters, blockedSelectors) {\n    var message = 'DOM blockers debug:\\n```';\n    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {\n        var filterName = _a[_i];\n        message += \"\\n\".concat(filterName, \":\");\n        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {\n            var selector = _c[_b];\n            message += \"\\n  \".concat(blockedSelectors[selector] ? '🚫' : '➡️', \" \").concat(selector);\n        }\n    }\n    // console.log is ok here because it's under a debug clause\n    // eslint-disable-next-line no-console\n    console.log(\"\".concat(message, \"\\n```\"));\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/color-gamut\n */\nfunction getColorGamut() {\n    // rec2020 includes p3 and p3 includes srgb\n    for (var _i = 0, _a = ['rec2020', 'p3', 'srgb']; _i < _a.length; _i++) {\n        var gamut = _a[_i];\n        if (matchMedia(\"(color-gamut: \".concat(gamut, \")\")).matches) {\n            return gamut;\n        }\n    }\n    return undefined;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/inverted-colors\n */\nfunction areColorsInverted() {\n    if (doesMatch$5('inverted')) {\n        return true;\n    }\n    if (doesMatch$5('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$5(value) {\n    return matchMedia(\"(inverted-colors: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n */\nfunction areColorsForced() {\n    if (doesMatch$4('active')) {\n        return true;\n    }\n    if (doesMatch$4('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$4(value) {\n    return matchMedia(\"(forced-colors: \".concat(value, \")\")).matches;\n}\n\nvar maxValueToCheck = 100;\n/**\n * If the display is monochrome (e.g. black&white), the value will be ≥0 and will mean the number of bits per pixel.\n * If the display is not monochrome, the returned value will be 0.\n * If the browser doesn't support this feature, the returned value will be undefined.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/monochrome\n */\nfunction getMonochromeDepth() {\n    if (!matchMedia('(min-monochrome: 0)').matches) {\n        // The media feature isn't supported by the browser\n        return undefined;\n    }\n    // A variation of binary search algorithm can be used here.\n    // But since expected values are very small (≤10), there is no sense in adding the complexity.\n    for (var i = 0; i <= maxValueToCheck; ++i) {\n        if (matchMedia(\"(max-monochrome: \".concat(i, \")\")).matches) {\n            return i;\n        }\n    }\n    throw new Error('Too high value');\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#prefers-contrast\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-contrast\n */\nfunction getContrastPreference() {\n    if (doesMatch$3('no-preference')) {\n        return 0 /* ContrastPreference.None */;\n    }\n    // The sources contradict on the keywords. Probably 'high' and 'low' will never be implemented.\n    // Need to check it when all browsers implement the feature.\n    if (doesMatch$3('high') || doesMatch$3('more')) {\n        return 1 /* ContrastPreference.More */;\n    }\n    if (doesMatch$3('low') || doesMatch$3('less')) {\n        return -1 /* ContrastPreference.Less */;\n    }\n    if (doesMatch$3('forced')) {\n        return 10 /* ContrastPreference.ForcedColors */;\n    }\n    return undefined;\n}\nfunction doesMatch$3(value) {\n    return matchMedia(\"(prefers-contrast: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\n */\nfunction isMotionReduced() {\n    if (doesMatch$2('reduce')) {\n        return true;\n    }\n    if (doesMatch$2('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$2(value) {\n    return matchMedia(\"(prefers-reduced-motion: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-transparency\n */\nfunction isTransparencyReduced() {\n    if (doesMatch$1('reduce')) {\n        return true;\n    }\n    if (doesMatch$1('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$1(value) {\n    return matchMedia(\"(prefers-reduced-transparency: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#dynamic-range\n */\nfunction isHDR() {\n    if (doesMatch('high')) {\n        return true;\n    }\n    if (doesMatch('standard')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch(value) {\n    return matchMedia(\"(dynamic-range: \".concat(value, \")\")).matches;\n}\n\nvar M = Math; // To reduce the minified code size\nvar fallbackFn = function () { return 0; };\n/**\n * @see https://gitlab.torproject.org/legacy/trac/-/issues/13018\n * @see https://bugzilla.mozilla.org/show_bug.cgi?id=531915\n */\nfunction getMathFingerprint() {\n    // Native operations\n    var acos = M.acos || fallbackFn;\n    var acosh = M.acosh || fallbackFn;\n    var asin = M.asin || fallbackFn;\n    var asinh = M.asinh || fallbackFn;\n    var atanh = M.atanh || fallbackFn;\n    var atan = M.atan || fallbackFn;\n    var sin = M.sin || fallbackFn;\n    var sinh = M.sinh || fallbackFn;\n    var cos = M.cos || fallbackFn;\n    var cosh = M.cosh || fallbackFn;\n    var tan = M.tan || fallbackFn;\n    var tanh = M.tanh || fallbackFn;\n    var exp = M.exp || fallbackFn;\n    var expm1 = M.expm1 || fallbackFn;\n    var log1p = M.log1p || fallbackFn;\n    // Operation polyfills\n    var powPI = function (value) { return M.pow(M.PI, value); };\n    var acoshPf = function (value) { return M.log(value + M.sqrt(value * value - 1)); };\n    var asinhPf = function (value) { return M.log(value + M.sqrt(value * value + 1)); };\n    var atanhPf = function (value) { return M.log((1 + value) / (1 - value)) / 2; };\n    var sinhPf = function (value) { return M.exp(value) - 1 / M.exp(value) / 2; };\n    var coshPf = function (value) { return (M.exp(value) + 1 / M.exp(value)) / 2; };\n    var expm1Pf = function (value) { return M.exp(value) - 1; };\n    var tanhPf = function (value) { return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1); };\n    var log1pPf = function (value) { return M.log(1 + value); };\n    // Note: constant values are empirical\n    return {\n        acos: acos(0.123124234234234242),\n        acosh: acosh(1e308),\n        acoshPf: acoshPf(1e154),\n        asin: asin(0.123124234234234242),\n        asinh: asinh(1),\n        asinhPf: asinhPf(1),\n        atanh: atanh(0.5),\n        atanhPf: atanhPf(0.5),\n        atan: atan(0.5),\n        sin: sin(-1e300),\n        sinh: sinh(1),\n        sinhPf: sinhPf(1),\n        cos: cos(10.000000000123),\n        cosh: cosh(1),\n        coshPf: coshPf(1),\n        tan: tan(-1e300),\n        tanh: tanh(1),\n        tanhPf: tanhPf(1),\n        exp: exp(1),\n        expm1: expm1(1),\n        expm1Pf: expm1Pf(1),\n        log1p: log1p(10),\n        log1pPf: log1pPf(10),\n        powPI: powPI(-100),\n    };\n}\n\n/**\n * We use m or w because these two characters take up the maximum width.\n * Also there are a couple of ligatures.\n */\nvar defaultText = 'mmMwWLliI0fiflO&1';\n/**\n * Settings of text blocks to measure. The keys are random but persistent words.\n */\nvar presets = {\n    /**\n     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,\n     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.\n     */\n    default: [],\n    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */\n    apple: [{ font: '-apple-system-body' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    serif: [{ fontFamily: 'serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    sans: [{ fontFamily: 'sans-serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    mono: [{ fontFamily: 'monospace' }],\n    /**\n     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.\n     * The height can be 0 in Chrome on a retina display.\n     */\n    min: [{ fontSize: '1px' }],\n    /** Tells one OS from another in desktop Chrome. */\n    system: [{ fontFamily: 'system-ui' }],\n};\n/**\n * The result is a dictionary of the width of the text samples.\n * Heights aren't included because they give no extra entropy and are unstable.\n *\n * The result is very stable in IE 11, Edge 18 and Safari 14.\n * The result changes when the OS pixel density changes in Chromium 87. The real pixel density is required to solve,\n * but seems like it's impossible: https://stackoverflow.com/q/1713771/1118709.\n * The \"min\" and the \"mono\" (only on Windows) value may change when the page is zoomed in Firefox 87.\n */\nfunction getFontPreferences() {\n    return withNaturalFonts(function (document, container) {\n        var elements = {};\n        var sizes = {};\n        // First create all elements to measure. If the DOM steps below are done in a single cycle,\n        // browser will alternate tree modification and layout reading, that is very slow.\n        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {\n            var key = _a[_i];\n            var _b = presets[key], _c = _b[0], style = _c === void 0 ? {} : _c, _d = _b[1], text = _d === void 0 ? defaultText : _d;\n            var element = document.createElement('span');\n            element.textContent = text;\n            element.style.whiteSpace = 'nowrap';\n            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {\n                var name_1 = _f[_e];\n                var value = style[name_1];\n                if (value !== undefined) {\n                    element.style[name_1] = value;\n                }\n            }\n            elements[key] = element;\n            container.append(document.createElement('br'), element);\n        }\n        // Then measure the created elements\n        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {\n            var key = _h[_g];\n            sizes[key] = elements[key].getBoundingClientRect().width;\n        }\n        return sizes;\n    });\n}\n/**\n * Creates a DOM environment that provides the most natural font available, including Android OS font.\n * Measurements of the elements are zoom-independent.\n * Don't put a content to measure inside an absolutely positioned element.\n */\nfunction withNaturalFonts(action, containerWidthPx) {\n    if (containerWidthPx === void 0) { containerWidthPx = 4000; }\n    /*\n     * Requirements for Android Chrome to apply the system font size to a text inside an iframe:\n     * - The iframe mustn't have a `display: none;` style;\n     * - The text mustn't be positioned absolutely;\n     * - The text block must be wide enough.\n     *   2560px on some devices in portrait orientation for the biggest font size option (32px);\n     * - There must be much enough text to form a few lines (I don't know the exact numbers);\n     * - The text must have the `text-size-adjust: none` style. Otherwise the text will scale in \"Desktop site\" mode;\n     *\n     * Requirements for Android Firefox to apply the system font size to a text inside an iframe:\n     * - The iframe document must have a header: `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />`.\n     *   The only way to set it is to use the `srcdoc` attribute of the iframe;\n     * - The iframe content must get loaded before adding extra content with JavaScript;\n     *\n     * https://example.com as the iframe target always inherits Android font settings so it can be used as a reference.\n     *\n     * Observations on how page zoom affects the measurements:\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - macOS Safari 14.0: offsetWidth = 5% fluctuation;\n     * - macOS Safari 14.0: getBoundingClientRect = 5% fluctuation;\n     * - iOS Safari 9, 10, 11.0, 12.0: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - iOS Safari 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - iOS Safari 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - iOS Safari 14.0: offsetWidth = 100% reliable;\n     * - iOS Safari 14.0: getBoundingClientRect = 100% reliable;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + offsetWidth = 1px fluctuation;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + getBoundingClientRect = 100% reliable;\n     * - Chrome 87: offsetWidth = 1px fluctuation;\n     * - Chrome 87: getBoundingClientRect = 0.7px fluctuation;\n     * - Firefox 48, 51: offsetWidth = 10% fluctuation;\n     * - Firefox 48, 51: getBoundingClientRect = 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: offsetWidth = width 100% reliable, height 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: getBoundingClientRect = width 100% reliable, height 10%\n     *   fluctuation;\n     * - Android Chrome 86: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - Android Firefox 84: font size in accessibility settings changes all the CSS sizes, but offsetWidth and\n     *   getBoundingClientRect keep measuring with regular units, so the size reflects the font size setting and doesn't\n     *   fluctuate;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + getBoundingClientRect = reflects the zoom level;\n     * - IE 11, Edge 18: offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: getBoundingClientRect = 100% reliable;\n     */\n    return withIframe(function (_, iframeWindow) {\n        var iframeDocument = iframeWindow.document;\n        var iframeBody = iframeDocument.body;\n        var bodyStyle = iframeBody.style;\n        bodyStyle.width = \"\".concat(containerWidthPx, \"px\");\n        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = 'none';\n        // See the big comment above\n        if (isChromium()) {\n            iframeBody.style.zoom = \"\".concat(1 / iframeWindow.devicePixelRatio);\n        }\n        else if (isWebKit()) {\n            iframeBody.style.zoom = 'reset';\n        }\n        // See the big comment above\n        var linesOfText = iframeDocument.createElement('div');\n        linesOfText.textContent = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)([], Array((containerWidthPx / 20) << 0), true).map(function () { return 'word'; }).join(' ');\n        iframeBody.appendChild(linesOfText);\n        return action(iframeDocument, iframeBody);\n    }, '<!doctype html><html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">');\n}\n\nfunction isPdfViewerEnabled() {\n    return navigator.pdfViewerEnabled;\n}\n\n/**\n * Unlike most other architectures, on x86/x86-64 when floating-point instructions\n * have no NaN arguments, but produce NaN output, the output NaN has sign bit set.\n * We use it to distinguish x86/x86-64 from other architectures, by doing subtraction\n * of two infinities (must produce NaN per IEEE 754 standard).\n *\n * See https://codebrowser.bddppq.com/pytorch/pytorch/third_party/XNNPACK/src/init.c.html#79\n */\nfunction getArchitecture() {\n    var f = new Float32Array(1);\n    var u8 = new Uint8Array(f.buffer);\n    f[0] = Infinity;\n    f[0] = f[0] - f[0];\n    return u8[3];\n}\n\n/**\n * The return type is a union instead of the enum, because it's too challenging to embed the const enum into another\n * project. Turning it into a union is a simple and an elegant solution.\n */\nfunction getApplePayState() {\n    var ApplePaySession = window.ApplePaySession;\n    if (typeof (ApplePaySession === null || ApplePaySession === void 0 ? void 0 : ApplePaySession.canMakePayments) !== 'function') {\n        return -1 /* ApplePayState.NoAPI */;\n    }\n    if (willPrintConsoleError()) {\n        return -3 /* ApplePayState.NotAvailableInFrame */;\n    }\n    try {\n        return ApplePaySession.canMakePayments() ? 1 /* ApplePayState.Enabled */ : 0 /* ApplePayState.Disabled */;\n    }\n    catch (error) {\n        return getStateFromError(error);\n    }\n}\n/**\n * Starting from Safari 15 calling `ApplePaySession.canMakePayments()` produces this error message when FingerprintJS\n * runs in an iframe with a cross-origin parent page, and the iframe on that page has no allow=\"payment *\" attribute:\n *   Feature policy 'Payment' check failed for element with origin 'https://example.com' and allow attribute ''.\n * This function checks whether the error message is expected.\n *\n * We check for cross-origin parents, which is prone to false-positive results. Instead, we should check for allowed\n * feature/permission, but we can't because none of these API works in Safari yet:\n *   navigator.permissions.query({ name: ‘payment' })\n *   navigator.permissions.query({ name: ‘payment-handler' })\n *   document.featurePolicy\n */\nvar willPrintConsoleError = isAnyParentCrossOrigin;\nfunction getStateFromError(error) {\n    // See full expected error messages in the test\n    if (error instanceof Error && error.name === 'InvalidAccessError' && /\\bfrom\\b.*\\binsecure\\b/i.test(error.message)) {\n        return -2 /* ApplePayState.NotAvailableInInsecureContext */;\n    }\n    throw error;\n}\n\n/**\n * Checks whether the Safari's Privacy Preserving Ad Measurement setting is on.\n * The setting is on when the value is not undefined.\n * A.k.a. private click measurement, privacy-preserving ad attribution.\n *\n * Unfortunately, it doesn't work in mobile Safari.\n * Probably, it will start working in mobile Safari or stop working in desktop Safari later.\n * We've found no way to detect the setting state in mobile Safari. Help wanted.\n *\n * @see https://webkit.org/blog/11529/introducing-private-click-measurement-pcm/\n * @see https://developer.apple.com/videos/play/wwdc2021/10033\n */\nfunction getPrivateClickMeasurement() {\n    var _a;\n    var link = document.createElement('a');\n    var sourceId = (_a = link.attributionSourceId) !== null && _a !== void 0 ? _a : link.attributionsourceid;\n    return sourceId === undefined ? undefined : String(sourceId);\n}\n\n/** WebGl context is not available */\nvar STATUS_NO_GL_CONTEXT = -1;\n/** WebGL context `getParameter` method is not a function */\nvar STATUS_GET_PARAMETER_NOT_A_FUNCTION = -2;\nvar validContextParameters = new Set([\n    10752, 2849, 2884, 2885, 2886, 2928, 2929, 2930, 2931, 2932, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968,\n    2978, 3024, 3042, 3088, 3089, 3106, 3107, 32773, 32777, 32777, 32823, 32824, 32936, 32937, 32938, 32939, 32968, 32969,\n    32970, 32971, 3317, 33170, 3333, 3379, 3386, 33901, 33902, 34016, 34024, 34076, 3408, 3410, 3411, 3412, 3413, 3414,\n    3415, 34467, 34816, 34817, 34818, 34819, 34877, 34921, 34930, 35660, 35661, 35724, 35738, 35739, 36003, 36004, 36005,\n    36347, 36348, 36349, 37440, 37441, 37443, 7936, 7937, 7938,\n    // SAMPLE_ALPHA_TO_COVERAGE (32926) and SAMPLE_COVERAGE (32928) are excluded because they trigger a console warning\n    // in IE, Chrome ≤ 59 and Safari ≤ 13 and give no entropy.\n]);\nvar validExtensionParams = new Set([\n    34047,\n    35723,\n    36063,\n    34852,\n    34853,\n    34854,\n    34229,\n    36392,\n    36795,\n    38449, // MAX_VIEWS_OVR\n]);\nvar shaderTypes = ['FRAGMENT_SHADER', 'VERTEX_SHADER'];\nvar precisionTypes = ['LOW_FLOAT', 'MEDIUM_FLOAT', 'HIGH_FLOAT', 'LOW_INT', 'MEDIUM_INT', 'HIGH_INT'];\nvar rendererInfoExtensionName = 'WEBGL_debug_renderer_info';\nvar polygonModeExtensionName = 'WEBGL_polygon_mode';\n/**\n * Gets the basic and simple WebGL parameters\n */\nfunction getWebGlBasics(_a) {\n    var _b, _c, _d, _e, _f, _g;\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var debugExtension = shouldAvoidDebugRendererInfo() ? null : gl.getExtension(rendererInfoExtensionName);\n    return {\n        version: ((_b = gl.getParameter(gl.VERSION)) === null || _b === void 0 ? void 0 : _b.toString()) || '',\n        vendor: ((_c = gl.getParameter(gl.VENDOR)) === null || _c === void 0 ? void 0 : _c.toString()) || '',\n        vendorUnmasked: debugExtension ? (_d = gl.getParameter(debugExtension.UNMASKED_VENDOR_WEBGL)) === null || _d === void 0 ? void 0 : _d.toString() : '',\n        renderer: ((_e = gl.getParameter(gl.RENDERER)) === null || _e === void 0 ? void 0 : _e.toString()) || '',\n        rendererUnmasked: debugExtension ? (_f = gl.getParameter(debugExtension.UNMASKED_RENDERER_WEBGL)) === null || _f === void 0 ? void 0 : _f.toString() : '',\n        shadingLanguageVersion: ((_g = gl.getParameter(gl.SHADING_LANGUAGE_VERSION)) === null || _g === void 0 ? void 0 : _g.toString()) || '',\n    };\n}\n/**\n * Gets the advanced and massive WebGL parameters and extensions\n */\nfunction getWebGlExtensions(_a) {\n    var cache = _a.cache;\n    var gl = getWebGLContext(cache);\n    if (!gl) {\n        return STATUS_NO_GL_CONTEXT;\n    }\n    if (!isValidParameterGetter(gl)) {\n        return STATUS_GET_PARAMETER_NOT_A_FUNCTION;\n    }\n    var extensions = gl.getSupportedExtensions();\n    var contextAttributes = gl.getContextAttributes();\n    var unsupportedExtensions = [];\n    // Features\n    var attributes = [];\n    var parameters = [];\n    var extensionParameters = [];\n    var shaderPrecisions = [];\n    // Context attributes\n    if (contextAttributes) {\n        for (var _i = 0, _b = Object.keys(contextAttributes); _i < _b.length; _i++) {\n            var attributeName = _b[_i];\n            attributes.push(\"\".concat(attributeName, \"=\").concat(contextAttributes[attributeName]));\n        }\n    }\n    // Context parameters\n    var constants = getConstantsFromPrototype(gl);\n    for (var _c = 0, constants_1 = constants; _c < constants_1.length; _c++) {\n        var constant = constants_1[_c];\n        var code = gl[constant];\n        parameters.push(\"\".concat(constant, \"=\").concat(code).concat(validContextParameters.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n    }\n    // Extension parameters\n    if (extensions) {\n        for (var _d = 0, extensions_1 = extensions; _d < extensions_1.length; _d++) {\n            var name_1 = extensions_1[_d];\n            if ((name_1 === rendererInfoExtensionName && shouldAvoidDebugRendererInfo()) ||\n                (name_1 === polygonModeExtensionName && shouldAvoidPolygonModeExtensions())) {\n                continue;\n            }\n            var extension = gl.getExtension(name_1);\n            if (!extension) {\n                unsupportedExtensions.push(name_1);\n                continue;\n            }\n            for (var _e = 0, _f = getConstantsFromPrototype(extension); _e < _f.length; _e++) {\n                var constant = _f[_e];\n                var code = extension[constant];\n                extensionParameters.push(\"\".concat(constant, \"=\").concat(code).concat(validExtensionParams.has(code) ? \"=\".concat(gl.getParameter(code)) : ''));\n            }\n        }\n    }\n    // Shader precision\n    for (var _g = 0, shaderTypes_1 = shaderTypes; _g < shaderTypes_1.length; _g++) {\n        var shaderType = shaderTypes_1[_g];\n        for (var _h = 0, precisionTypes_1 = precisionTypes; _h < precisionTypes_1.length; _h++) {\n            var precisionType = precisionTypes_1[_h];\n            var shaderPrecision = getShaderPrecision(gl, shaderType, precisionType);\n            shaderPrecisions.push(\"\".concat(shaderType, \".\").concat(precisionType, \"=\").concat(shaderPrecision.join(',')));\n        }\n    }\n    // Postprocess\n    extensionParameters.sort();\n    parameters.sort();\n    return {\n        contextAttributes: attributes,\n        parameters: parameters,\n        shaderPrecisions: shaderPrecisions,\n        extensions: extensions,\n        extensionParameters: extensionParameters,\n        unsupportedExtensions: unsupportedExtensions,\n    };\n}\n/**\n * This function usually takes the most time to execute in all the sources, therefore we cache its result.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getWebGLContext(cache) {\n    if (cache.webgl) {\n        return cache.webgl.context;\n    }\n    var canvas = document.createElement('canvas');\n    var context;\n    canvas.addEventListener('webglCreateContextError', function () { return (context = undefined); });\n    for (var _i = 0, _a = ['webgl', 'experimental-webgl']; _i < _a.length; _i++) {\n        var type = _a[_i];\n        try {\n            context = canvas.getContext(type);\n        }\n        catch (_b) {\n            // Ok, continue\n        }\n        if (context) {\n            break;\n        }\n    }\n    cache.webgl = { context: context };\n    return context;\n}\n/**\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLShaderPrecisionFormat\n * https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getShaderPrecisionFormat\n * https://www.khronos.org/registry/webgl/specs/latest/1.0/#5.12\n */\nfunction getShaderPrecision(gl, shaderType, precisionType) {\n    var shaderPrecision = gl.getShaderPrecisionFormat(gl[shaderType], gl[precisionType]);\n    return shaderPrecision ? [shaderPrecision.rangeMin, shaderPrecision.rangeMax, shaderPrecision.precision] : [];\n}\nfunction getConstantsFromPrototype(obj) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    var keys = Object.keys(obj.__proto__);\n    return keys.filter(isConstantLike);\n}\nfunction isConstantLike(key) {\n    return typeof key === 'string' && !key.match(/[^A-Z0-9_x]/);\n}\n/**\n * Some browsers print a console warning when the WEBGL_debug_renderer_info extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidDebugRendererInfo() {\n    return isGecko();\n}\n/**\n * Some browsers print a console warning when the WEBGL_polygon_mode extension is requested.\n * JS Agent aims to avoid printing messages to console, so we avoid this extension in that browsers.\n */\nfunction shouldAvoidPolygonModeExtensions() {\n    return isChromium() || isWebKit();\n}\n/**\n * Some unknown browsers have no `getParameter` method\n */\nfunction isValidParameterGetter(gl) {\n    return typeof gl.getParameter === 'function';\n}\n\nfunction getAudioContextBaseLatency() {\n    // The signal emits warning in Chrome and Firefox, therefore it is enabled on Safari where it doesn't produce warning\n    // and on Android where it's less visible\n    var isAllowedPlatform = isAndroid() || isWebKit();\n    if (!isAllowedPlatform) {\n        return -2 /* SpecialFingerprint.Disabled */;\n    }\n    if (!window.AudioContext) {\n        return -1 /* SpecialFingerprint.NotSupported */;\n    }\n    var latency = new AudioContext().baseLatency;\n    if (latency === null || latency === undefined) {\n        return -1 /* SpecialFingerprint.NotSupported */;\n    }\n    if (!isFinite(latency)) {\n        return -3 /* SpecialFingerprint.NotFinite */;\n    }\n    return latency;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions\n *\n * The return type is a union instead of a const enum due to the difficulty of embedding const enums in other projects.\n * This makes integration simpler and more elegant.\n */\nfunction getDateTimeLocale() {\n    if (!window.Intl) {\n        return -1 /* Status.IntlAPINotSupported */;\n    }\n    var DateTimeFormat = window.Intl.DateTimeFormat;\n    if (!DateTimeFormat) {\n        return -2 /* Status.DateTimeFormatNotSupported */;\n    }\n    var locale = DateTimeFormat().resolvedOptions().locale;\n    if (!locale && locale !== '') {\n        return -3 /* Status.LocaleNotAvailable */;\n    }\n    return locale;\n}\n\n/**\n * The list of entropy sources used to make visitor identifiers.\n *\n * This value isn't restricted by Semantic Versioning, i.e. it may be changed without bumping minor or major version of\n * this package.\n *\n * Note: Rollup and Webpack are smart enough to remove unused properties of this object during tree-shaking, so there is\n * no need to export the sources individually.\n */\nvar sources = {\n    // READ FIRST:\n    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-add-an-entropy-source\n    // to learn how entropy source works and how to make your own.\n    // The sources run in this exact order.\n    // The asynchronous sources are at the start to run in parallel with other sources.\n    fonts: getFonts,\n    domBlockers: getDomBlockers,\n    fontPreferences: getFontPreferences,\n    audio: getAudioFingerprint,\n    screenFrame: getScreenFrame,\n    canvas: getCanvasFingerprint,\n    osCpu: getOsCpu,\n    languages: getLanguages,\n    colorDepth: getColorDepth,\n    deviceMemory: getDeviceMemory,\n    screenResolution: getScreenResolution,\n    hardwareConcurrency: getHardwareConcurrency,\n    timezone: getTimezone,\n    sessionStorage: getSessionStorage,\n    localStorage: getLocalStorage,\n    indexedDB: getIndexedDB,\n    openDatabase: getOpenDatabase,\n    cpuClass: getCpuClass,\n    platform: getPlatform,\n    plugins: getPlugins,\n    touchSupport: getTouchSupport,\n    vendor: getVendor,\n    vendorFlavors: getVendorFlavors,\n    cookiesEnabled: areCookiesEnabled,\n    colorGamut: getColorGamut,\n    invertedColors: areColorsInverted,\n    forcedColors: areColorsForced,\n    monochrome: getMonochromeDepth,\n    contrast: getContrastPreference,\n    reducedMotion: isMotionReduced,\n    reducedTransparency: isTransparencyReduced,\n    hdr: isHDR,\n    math: getMathFingerprint,\n    pdfViewerEnabled: isPdfViewerEnabled,\n    architecture: getArchitecture,\n    applePay: getApplePayState,\n    privateClickMeasurement: getPrivateClickMeasurement,\n    audioBaseLatency: getAudioContextBaseLatency,\n    dateTimeLocale: getDateTimeLocale,\n    // Some sources can affect other sources (e.g. WebGL can affect canvas), so it's important to run these sources\n    // after other sources.\n    webGlBasics: getWebGlBasics,\n    webGlExtensions: getWebGlExtensions,\n};\n/**\n * Loads the built-in entropy sources.\n * Returns a function that collects the entropy components to make the visitor identifier.\n */\nfunction loadBuiltinSources(options) {\n    return loadSources(sources, options, []);\n}\n\nvar commentTemplate = '$ if upgrade to Pro: https://fpjs.dev/pro';\nfunction getConfidence(components) {\n    var openConfidenceScore = getOpenConfidenceScore(components);\n    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);\n    return { score: openConfidenceScore, comment: commentTemplate.replace(/\\$/g, \"\".concat(proConfidenceScore)) };\n}\nfunction getOpenConfidenceScore(components) {\n    // In order to calculate the true probability of the visitor identifier being correct, we need to know the number of\n    // website visitors (the higher the number, the less the probability because the fingerprint entropy is limited).\n    // JS agent doesn't know the number of visitors, so we can only do an approximate assessment.\n    if (isAndroid()) {\n        return 0.4;\n    }\n    // Safari (mobile and desktop)\n    if (isWebKit()) {\n        return isDesktopWebKit() && !(isWebKit616OrNewer() && isSafariWebKit()) ? 0.5 : 0.3;\n    }\n    var platform = 'value' in components.platform ? components.platform.value : '';\n    // Windows\n    if (/^Win/.test(platform)) {\n        // The score is greater than on macOS because of the higher variety of devices running Windows.\n        // Chrome provides more entropy than Firefox according too\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Windows%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.6;\n    }\n    // macOS\n    if (/^Mac/.test(platform)) {\n        // Chrome provides more entropy than Safari and Safari provides more entropy than Firefox.\n        // Chrome is more popular than Safari and Safari is more popular than Firefox according to\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Mac%20OS%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.5;\n    }\n    // Another platform, e.g. a desktop Linux. It's rare, so it should be pretty unique.\n    return 0.7;\n}\nfunction deriveProConfidenceScore(openConfidenceScore) {\n    return round(0.99 + 0.01 * openConfidenceScore, 0.0001);\n}\n\nfunction componentsToCanonicalString(components) {\n    var result = '';\n    for (var _i = 0, _a = Object.keys(components).sort(); _i < _a.length; _i++) {\n        var componentKey = _a[_i];\n        var component = components[componentKey];\n        var value = 'error' in component ? 'error' : JSON.stringify(component.value);\n        result += \"\".concat(result ? '|' : '').concat(componentKey.replace(/([:|\\\\])/g, '\\\\$1'), \":\").concat(value);\n    }\n    return result;\n}\nfunction componentsToDebugString(components) {\n    return JSON.stringify(components, function (_key, value) {\n        if (value instanceof Error) {\n            return errorToObject(value);\n        }\n        return value;\n    }, 2);\n}\nfunction hashComponents(components) {\n    return x64hash128(componentsToCanonicalString(components));\n}\n/**\n * Makes a GetResult implementation that calculates the visitor id hash on demand.\n * Designed for optimisation.\n */\nfunction makeLazyGetResult(components) {\n    var visitorIdCache;\n    // This function runs very fast, so there is no need to make it lazy\n    var confidence = getConfidence(components);\n    // A plain class isn't used because its getters and setters aren't enumerable.\n    return {\n        get visitorId() {\n            if (visitorIdCache === undefined) {\n                visitorIdCache = hashComponents(this.components);\n            }\n            return visitorIdCache;\n        },\n        set visitorId(visitorId) {\n            visitorIdCache = visitorId;\n        },\n        confidence: confidence,\n        components: components,\n        version: version,\n    };\n}\n/**\n * A delay is required to ensure consistent entropy components.\n * See https://github.com/fingerprintjs/fingerprintjs/issues/254\n * and https://github.com/fingerprintjs/fingerprintjs/issues/307\n * and https://github.com/fingerprintjs/fingerprintjs/commit/945633e7c5f67ae38eb0fea37349712f0e669b18\n */\nfunction prepareForSources(delayFallback) {\n    if (delayFallback === void 0) { delayFallback = 50; }\n    // A proper deadline is unknown. Let it be twice the fallback timeout so that both cases have the same average time.\n    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);\n}\n/**\n * The function isn't exported from the index file to not allow to call it without `load()`.\n * The hiding gives more freedom for future non-breaking updates.\n *\n * A factory function is used instead of a class to shorten the attribute names in the minified code.\n * Native private class fields could've been used, but TypeScript doesn't allow them with `\"target\": \"es5\"`.\n */\nfunction makeAgent(getComponents, debug) {\n    var creationTime = Date.now();\n    return {\n        get: function (options) {\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n                var startTime, components, result;\n                return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            startTime = Date.now();\n                            return [4 /*yield*/, getComponents()];\n                        case 1:\n                            components = _a.sent();\n                            result = makeLazyGetResult(components);\n                            if (debug || (options === null || options === void 0 ? void 0 : options.debug)) {\n                                // console.log is ok here because it's under a debug clause\n                                // eslint-disable-next-line no-console\n                                console.log(\"Copy the text below to get the debug data:\\n\\n```\\nversion: \".concat(result.version, \"\\nuserAgent: \").concat(navigator.userAgent, \"\\ntimeBetweenLoadAndGet: \").concat(startTime - creationTime, \"\\nvisitorId: \").concat(result.visitorId, \"\\ncomponents: \").concat(componentsToDebugString(components), \"\\n```\"));\n                            }\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        },\n    };\n}\n/**\n * Sends an unpersonalized AJAX request to collect installation statistics\n */\nfunction monitor() {\n    // The FingerprintJS CDN (https://github.com/fingerprintjs/cdn) replaces `window.__fpjs_d_m` with `true`\n    if (window.__fpjs_d_m || Math.random() >= 0.001) {\n        return;\n    }\n    try {\n        var request = new XMLHttpRequest();\n        request.open('get', \"https://m1.openfpcdn.io/fingerprintjs/v\".concat(version, \"/npm-monitoring\"), true);\n        request.send();\n    }\n    catch (error) {\n        // console.error is ok here because it's an unexpected error handler\n        // eslint-disable-next-line no-console\n        console.error(error);\n    }\n}\n/**\n * Builds an instance of Agent and waits a delay required for a proper operation.\n */\nfunction load(options) {\n    var _a;\n    if (options === void 0) { options = {}; }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var delayFallback, debug, getComponents;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    if ((_a = options.monitoring) !== null && _a !== void 0 ? _a : true) {\n                        monitor();\n                    }\n                    delayFallback = options.delayFallback, debug = options.debug;\n                    return [4 /*yield*/, prepareForSources(delayFallback)];\n                case 1:\n                    _b.sent();\n                    getComponents = loadBuiltinSources({ cache: {}, debug: debug });\n                    return [2 /*return*/, makeAgent(getComponents, debug)];\n            }\n        });\n    });\n}\n\n// The default export is a syntax sugar (`import * as FP from '...' → import FP from '...'`).\n// It should contain all the public exported values.\nvar index = { load: load, hashComponents: hashComponents, componentsToDebugString: componentsToDebugString };\n// The exports below are for private usage. They may change unexpectedly. Use them at your own risk.\n/** Not documented, out of Semantic Versioning, usage is at your own risk */\nvar murmurX64Hash128 = x64hash128;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js\n");

/***/ })

};
;