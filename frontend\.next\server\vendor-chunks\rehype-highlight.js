"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-highlight";
exports.ids = ["vendor-chunks/rehype-highlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-highlight/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/rehype-highlight/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeHighlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/common.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @import {ElementContent, Element, Root} from 'hast'\n * @import {LanguageFn} from 'lowlight'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {Readonly<Record<string, ReadonlyArray<string> | string>> | null | undefined} [aliases={}]\n *   Register more aliases (optional);\n *   passed to `lowlight.registerAlias`.\n * @property {boolean | null | undefined} [detect=false]\n *   Highlight code without language classes by guessing its programming\n *   language (default: `false`).\n * @property {Readonly<Record<string, LanguageFn>> | null | undefined} [languages]\n *   Register languages (default: `common`);\n *   passed to `lowlight.register`.\n * @property {ReadonlyArray<string> | null | undefined} [plainText=[]]\n *   List of language names to not highlight (optional);\n *   note you can also add `no-highlight` classes.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   Names of languages to check when detecting (default: all registered\n *   languages).\n */\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Apply syntax highlighting.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeHighlight(options) {\n  const settings = options || emptyOptions\n  const aliases = settings.aliases\n  const detect = settings.detect || false\n  const languages = settings.languages || lowlight__WEBPACK_IMPORTED_MODULE_0__.grammars\n  const plainText = settings.plainText\n  const prefix = settings.prefix\n  const subset = settings.subset\n  let name = 'hljs'\n\n  const lowlight = (0,lowlight__WEBPACK_IMPORTED_MODULE_1__.createLowlight)(languages)\n\n  if (aliases) {\n    lowlight.registerAlias(aliases)\n  }\n\n  if (prefix) {\n    const pos = prefix.indexOf('-')\n    name = pos > -1 ? prefix.slice(0, pos) : prefix\n  }\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, 'element', function (node, _, parent) {\n      if (\n        node.tagName !== 'code' ||\n        !parent ||\n        parent.type !== 'element' ||\n        parent.tagName !== 'pre'\n      ) {\n        return\n      }\n\n      const lang = language(node)\n\n      if (\n        lang === false ||\n        (!lang && !detect) ||\n        (lang && plainText && plainText.includes(lang))\n      ) {\n        return\n      }\n\n      if (!Array.isArray(node.properties.className)) {\n        node.properties.className = []\n      }\n\n      if (!node.properties.className.includes(name)) {\n        node.properties.className.unshift(name)\n      }\n\n      /** @type {Root} */\n      let result\n\n      try {\n        result = lang\n          ? lowlight.highlight(lang, (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(parent), {prefix})\n          : lowlight.highlightAuto((0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(parent), {prefix, subset})\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n\n        if (lang && /Unknown language/.test(cause.message)) {\n          file.message(\n            'Cannot highlight as `' + lang + '`, it’s not registered',\n            {\n              ancestors: [parent, node],\n              cause,\n              place: node.position,\n              ruleId: 'missing-language',\n              source: 'rehype-highlight'\n            }\n          )\n\n          /* c8 ignore next 5 -- throw arbitrary hljs errors */\n          return\n        }\n\n        throw cause\n      }\n\n      if (!lang && result.data && result.data.language) {\n        node.properties.className.push('language-' + result.data.language)\n      }\n\n      if (result.children.length > 0) {\n        node.children = /** @type {Array<ElementContent>} */ (result.children)\n      }\n    })\n  }\n}\n\n/**\n * Get the programming language of `node`.\n *\n * @param {Element} node\n *   Node.\n * @returns {false | string | undefined}\n *   Language or `undefined`, or `false` when an explikcit `no-highlight` class\n *   is used.\n */\nfunction language(node) {\n  const list = node.properties.className\n  let index = -1\n\n  if (!Array.isArray(list)) {\n    return\n  }\n\n  /** @type {string | undefined} */\n  let name\n\n  while (++index < list.length) {\n    const value = String(list[index])\n\n    if (value === 'no-highlight' || value === 'nohighlight') {\n      return false\n    }\n\n    if (!name && value.slice(0, 5) === 'lang-') {\n      name = value.slice(5)\n    }\n\n    if (!name && value.slice(0, 9) === 'language-') {\n      name = value.slice(9)\n    }\n  }\n\n  return name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-highlight/lib/index.js\n");

/***/ })

};
;