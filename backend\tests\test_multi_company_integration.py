#!/usr/bin/env python3
"""
Comprehensive integration test for multi-company functionality.
This script tests the complete multi-company implementation end-to-end.
"""

import sys
import os
import sqlite3
import json
import uuid
import requests
import time
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from ai360.api.migrations.add_company_support import CompanySupportMigration
from ai360.api.models.company import CompanyDatabase
from ai360.api.models.project import ProjectDatabase
from ai360.api.models.user_preferences import UserPreferencesDatabase


class MultiCompanyIntegrationTest:
    """Comprehensive integration test for multi-company functionality."""
    
    def __init__(self, api_base_url="http://localhost:8000"):
        self.api_base_url = api_base_url
        self.test_data = {
            "users": ["test_user_1", "test_user_2", "test_user_3"],
            "companies": [],
            "projects": [],
            "access_grants": []
        }
        
    def run_all_tests(self):
        """Run all integration tests."""
        print("🧪 Starting Multi-Company Integration Tests")
        print("=" * 60)
        
        try:
            # Test 1: Database Migration
            self.test_database_migration()
            
            # Test 2: Company Management
            self.test_company_management()
            
            # Test 3: User Access Control
            self.test_user_access_control()
            
            # Test 4: Data Isolation
            self.test_data_isolation()
            
            # Test 5: Project Company Scoping
            self.test_project_company_scoping()
            
            # Test 6: User Preferences Company Scoping
            self.test_user_preferences_company_scoping()
            
            # Test 7: API Integration
            self.test_api_integration()
            
            # Test 8: Company Hierarchy
            self.test_company_hierarchy()
            
            print("\n🎉 All integration tests passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Integration test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_database_migration(self):
        """Test database migration functionality."""
        print("\n📋 Test 1: Database Migration")
        
        # Create test data directory
        test_data_dir = backend_dir / "test_data"
        test_data_dir.mkdir(exist_ok=True)
        
        # Create old schema databases
        self._create_old_schema_test_data(test_data_dir)
        
        # Run migration
        migration = CompanySupportMigration(str(test_data_dir))
        success = migration.run_migration()
        
        if not success:
            raise Exception("Database migration failed")
        
        # Verify migration results
        self._verify_migration_results(test_data_dir)
        
        print("   ✅ Database migration successful")
    
    def test_company_management(self):
        """Test company CRUD operations."""
        print("\n🏢 Test 2: Company Management")
        
        company_db = CompanyDatabase()
        
        # Test company creation
        company1 = company_db.create_company(
            name="Test Company 1",
            description="First test company",
            primary_color="#3B82F6",
            secondary_color="#1E40AF",
            created_by=self.test_data["users"][0]
        )
        
        if not company1:
            raise Exception("Failed to create company 1")
        
        self.test_data["companies"].append(company1)
        
        company2 = company_db.create_company(
            name="Test Company 2",
            description="Second test company",
            primary_color="#10B981",
            secondary_color="#059669",
            created_by=self.test_data["users"][1]
        )
        
        if not company2:
            raise Exception("Failed to create company 2")
        
        self.test_data["companies"].append(company2)
        
        # Test company retrieval
        retrieved_company = company_db.get_company_by_id(company1["id"])
        if not retrieved_company or retrieved_company["name"] != company1["name"]:
            raise Exception("Failed to retrieve company")
        
        # Test company update
        updates = {"description": "Updated description"}
        success = company_db.update_company(company1["id"], updates)
        if not success:
            raise Exception("Failed to update company")
        
        # Test duplicate name prevention
        duplicate_company = company_db.create_company(
            name="Test Company 1",  # Same name as company1
            created_by=self.test_data["users"][2]
        )
        if duplicate_company:
            raise Exception("Duplicate company name was allowed")
        
        print("   ✅ Company CRUD operations successful")
    
    def test_user_access_control(self):
        """Test user access control functionality."""
        print("\n👥 Test 3: User Access Control")
        
        company_db = CompanyDatabase()
        company1 = self.test_data["companies"][0]
        company2 = self.test_data["companies"][1]
        
        # Test granting access
        success = company_db.grant_user_access(
            user_id=self.test_data["users"][2],
            company_id=company1["id"],
            role="member",
            granted_by=self.test_data["users"][0]
        )
        
        if not success:
            raise Exception("Failed to grant user access")
        
        # Test user company retrieval
        user_companies = company_db.get_companies_for_user(self.test_data["users"][2])
        if len(user_companies) != 1 or user_companies[0]["id"] != company1["id"]:
            raise Exception("User companies not retrieved correctly")
        
        # Test role verification
        if user_companies[0]["role"] != "member":
            raise Exception("User role not set correctly")
        
        # Test access revocation
        success = company_db.revoke_user_access(
            self.test_data["users"][2],
            company1["id"]
        )
        
        if not success:
            raise Exception("Failed to revoke user access")
        
        # Verify access was revoked
        user_companies = company_db.get_companies_for_user(self.test_data["users"][2])
        if len(user_companies) != 0:
            raise Exception("User access was not properly revoked")
        
        print("   ✅ User access control successful")
    
    def test_data_isolation(self):
        """Test data isolation between companies."""
        print("\n🔒 Test 4: Data Isolation")
        
        project_db = ProjectDatabase()
        company1 = self.test_data["companies"][0]
        company2 = self.test_data["companies"][1]
        
        # Create projects for different companies
        project1 = project_db.create_project(
            name="Company 1 Project",
            user_id=self.test_data["users"][0],
            company_id=company1["id"]
        )
        
        project2 = project_db.create_project(
            name="Company 2 Project",
            user_id=self.test_data["users"][1],
            company_id=company2["id"]
        )
        
        if not project1 or not project2:
            raise Exception("Failed to create test projects")
        
        self.test_data["projects"].extend([project1, project2])
        
        # Test company-scoped project retrieval
        company1_projects = project_db.get_projects_by_user_and_company(
            self.test_data["users"][0],
            company1["id"]
        )
        
        company2_projects = project_db.get_projects_by_user_and_company(
            self.test_data["users"][1],
            company2["id"]
        )
        
        # Verify isolation
        if len(company1_projects) != 1 or company1_projects[0]["id"] != project1["id"]:
            raise Exception("Company 1 projects not isolated correctly")
        
        if len(company2_projects) != 1 or company2_projects[0]["id"] != project2["id"]:
            raise Exception("Company 2 projects not isolated correctly")
        
        # Test cross-company access prevention
        cross_company_projects = project_db.get_projects_by_user_and_company(
            self.test_data["users"][0],
            company2["id"]  # User 0 trying to access Company 2 projects
        )
        
        if len(cross_company_projects) != 0:
            raise Exception("Cross-company data access was not prevented")
        
        print("   ✅ Data isolation successful")
    
    def test_project_company_scoping(self):
        """Test project company scoping functionality."""
        print("\n📁 Test 5: Project Company Scoping")
        
        project_db = ProjectDatabase()
        company1 = self.test_data["companies"][0]
        
        # Test project creation with company_id
        project = project_db.create_project(
            name="Scoped Project",
            user_id=self.test_data["users"][0],
            company_id=company1["id"],
            status="Writing",
            privacy="Shared"
        )
        
        if not project or project["company_id"] != company1["id"]:
            raise Exception("Project company scoping failed")
        
        # Test project update maintains company_id
        success = project_db.update_project(
            project["id"],
            {"name": "Updated Scoped Project"}
        )
        
        if not success:
            raise Exception("Failed to update scoped project")
        
        updated_project = project_db.get_project(project["id"])
        if updated_project["company_id"] != company1["id"]:
            raise Exception("Project company_id was lost during update")
        
        print("   ✅ Project company scoping successful")
    
    def test_user_preferences_company_scoping(self):
        """Test user preferences company scoping."""
        print("\n⚙️ Test 6: User Preferences Company Scoping")
        
        prefs_db = UserPreferencesDatabase()
        company1 = self.test_data["companies"][0]
        company2 = self.test_data["companies"][1]
        user = self.test_data["users"][0]
        
        # Test theme preferences with company scoping
        theme_prefs1 = {
            "mode": "dark",
            "color_scheme": "blue",
            "auto_switch_enabled": False
        }
        
        theme_prefs2 = {
            "mode": "light",
            "color_scheme": "green",
            "auto_switch_enabled": True
        }
        
        # Save preferences for different companies
        success1 = prefs_db.save_theme_preferences(user, company1["id"], theme_prefs1)
        success2 = prefs_db.save_theme_preferences(user, company2["id"], theme_prefs2)
        
        if not success1 or not success2:
            raise Exception("Failed to save company-scoped theme preferences")
        
        # Retrieve and verify preferences are company-specific
        retrieved_prefs1 = prefs_db.get_theme_preferences(user, company1["id"])
        retrieved_prefs2 = prefs_db.get_theme_preferences(user, company2["id"])
        
        if (retrieved_prefs1["mode"] != "dark" or 
            retrieved_prefs2["mode"] != "light"):
            raise Exception("Company-scoped preferences not isolated correctly")
        
        print("   ✅ User preferences company scoping successful")
    
    def test_api_integration(self):
        """Test API integration with company context."""
        print("\n🌐 Test 7: API Integration")
        
        # Test if API server is running
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            if response.status_code != 200:
                print("   ⚠️ API server not running, skipping API tests")
                return
        except requests.exceptions.RequestException:
            print("   ⚠️ API server not accessible, skipping API tests")
            return
        
        # Test company creation via API
        company_data = {
            "name": "API Test Company",
            "description": "Created via API test",
            "primary_color": "#F59E0B",
            "secondary_color": "#D97706"
        }
        
        response = requests.post(
            f"{self.api_base_url}/api/companies?created_by={self.test_data['users'][0]}",
            json=company_data,
            timeout=10
        )
        
        if response.status_code != 200:
            raise Exception(f"API company creation failed: {response.status_code}")
        
        api_company = response.json()
        
        # Test user companies retrieval via API
        response = requests.get(
            f"{self.api_base_url}/api/users/{self.test_data['users'][0]}/companies",
            timeout=10
        )
        
        if response.status_code != 200:
            raise Exception(f"API user companies retrieval failed: {response.status_code}")
        
        user_companies = response.json()
        if "companies" not in user_companies:
            raise Exception("API response missing companies field")
        
        print("   ✅ API integration successful")
    
    def test_company_hierarchy(self):
        """Test company hierarchy functionality."""
        print("\n🏗️ Test 8: Company Hierarchy")
        
        company_db = CompanyDatabase()
        
        # Create parent company
        parent_company = company_db.create_company(
            name="Parent Corporation",
            description="Parent company for testing hierarchy",
            created_by=self.test_data["users"][0]
        )
        
        if not parent_company:
            raise Exception("Failed to create parent company")
        
        # Create subsidiary company
        subsidiary_company = company_db.create_company(
            name="Subsidiary Inc",
            description="Subsidiary company for testing hierarchy",
            parent_company_id=parent_company["id"],
            created_by=self.test_data["users"][0]
        )
        
        if not subsidiary_company:
            raise Exception("Failed to create subsidiary company")
        
        # Verify hierarchy relationship
        if subsidiary_company["parent_company_id"] != parent_company["id"]:
            raise Exception("Subsidiary parent relationship not set correctly")
        
        # Test subsidiary retrieval
        subsidiaries = company_db.get_subsidiaries(parent_company["id"])
        if len(subsidiaries) != 1 or subsidiaries[0]["id"] != subsidiary_company["id"]:
            raise Exception("Subsidiary retrieval failed")
        
        print("   ✅ Company hierarchy successful")
    
    def _create_old_schema_test_data(self, data_dir):
        """Create test data with old schema (before migration)."""
        # Create projects database with old schema
        projects_db_path = data_dir / "projects.db"
        with sqlite3.connect(projects_db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    client TEXT,
                    status TEXT NOT NULL,
                    privacy TEXT NOT NULL DEFAULT 'Private',
                    start_date TEXT,
                    deadline TEXT,
                    summary TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    metadata TEXT DEFAULT '{}'
                )
            """)
            
            # Insert test data
            now = datetime.utcnow().isoformat()
            conn.execute("""
                INSERT INTO projects (
                    id, name, client, status, privacy, start_date, deadline,
                    summary, created_at, updated_at, user_id, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                str(uuid.uuid4()), "Legacy Project", "Legacy Client", "Preparing", "Private",
                None, None, "Legacy project summary", now, now, "legacy_user", "{}"
            ))
            
            conn.commit()
    
    def _verify_migration_results(self, data_dir):
        """Verify that migration was successful."""
        companies_db_path = data_dir / "companies.db"
        projects_db_path = data_dir / "projects.db"
        
        # Check companies database exists
        if not companies_db_path.exists():
            raise Exception("Companies database not created")
        
        # Check default company was created
        with sqlite3.connect(companies_db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM companies WHERE name = 'Default Company'")
            count = cursor.fetchone()[0]
            if count != 1:
                raise Exception("Default company not created")
        
        # Check projects table has company_id
        with sqlite3.connect(projects_db_path) as conn:
            cursor = conn.execute("PRAGMA table_info(projects)")
            columns = [row[1] for row in cursor.fetchall()]
            if "company_id" not in columns:
                raise Exception("Projects table not updated with company_id")


def main():
    """Main test function."""
    test = MultiCompanyIntegrationTest()
    success = test.run_all_tests()
    
    if success:
        print("\n🎉 All multi-company integration tests passed!")
        print("The multi-company implementation is working correctly.")
    else:
        print("\n❌ Some integration tests failed!")
        print("Please check the implementation and fix any issues.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
