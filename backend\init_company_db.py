#!/usr/bin/env python3
"""
Initialize the company database manually.
"""

import sqlite3
import os
from pathlib import Path

def init_company_database():
    """Initialize the company database."""
    # Create the database path
    backend_dir = Path(__file__).parent
    db_path = backend_dir / "data" / "companies.db"
    
    # Ensure the data directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    print(f"Creating database at: {db_path}")
    
    try:
        with sqlite3.connect(str(db_path)) as conn:
            # Create companies table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS companies (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    logo_url TEXT,
                    primary_color TEXT DEFAULT '#3B82F6',
                    secondary_color TEXT DEFAULT '#1E40AF',
                    parent_company_id TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    settings TEXT DEFAULT '{}',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    created_by TEXT NOT NULL,
                    metadata TEXT DEFAULT '{}',
                    FOREIGN KEY (parent_company_id) REFERENCES companies(id)
                )
            """)
            
            # Create indexes
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_companies_name 
                ON companies(name)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_companies_parent 
                ON companies(parent_company_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_companies_active 
                ON companies(is_active)
            """)
            
            # Create user-company access table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_company_access (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'member',
                    granted_at TEXT NOT NULL,
                    granted_by TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (company_id) REFERENCES companies(id),
                    UNIQUE(user_id, company_id)
                )
            """)
            
            # Create indexes for user-company access
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_user_company_user 
                ON user_company_access(user_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_user_company_company 
                ON user_company_access(company_id)
            """)
            
            conn.commit()
            print("✅ Database initialized successfully!")
            
            # Test the database
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"✅ Created tables: {[table[0] for table in tables]}")
            
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("Initializing Company Database")
    print("=" * 40)
    
    success = init_company_database()
    
    if success:
        print("\n🎉 Company database initialized successfully!")
    else:
        print("\n❌ Failed to initialize company database.")
