"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@next";
exports.ids = ["vendor-chunks/@next"];
exports.modules = {

/***/ "(ssr)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":
/*!************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = ThirdPartyScriptEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction ThirdPartyScriptEmbed({ html, height = null, width = null, children, dataNtpc = \"\" }) {\n    (0, react_1.useEffect)(()=>{\n        if (dataNtpc) {\n            // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n            // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n            // existing API.\n            performance.mark(\"mark_feature_usage\", {\n                detail: {\n                    feature: `next-third-parties-${dataNtpc}`\n                }\n            });\n        }\n    }, [\n        dataNtpc\n    ]);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            children,\n            html ? (0, jsx_runtime_1.jsx)(\"div\", {\n                style: {\n                    height: height != null ? `${height}px` : \"auto\",\n                    width: width != null ? `${width}px` : \"auto\"\n                },\n                \"data-ntpc\": dataNtpc,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                }\n            }) : null\n        ]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@next/third-parties/dist/google/ga.js":
/*!************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/ga.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GoogleAnalytics = GoogleAnalytics;\nexports.sendGAEvent = sendGAEvent;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = undefined;\nfunction GoogleAnalytics(props) {\n    const { gaId, debugMode, dataLayerName = \"dataLayer\", nonce } = props;\n    if (currDataLayerName === undefined) {\n        currDataLayerName = dataLayerName;\n    }\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark(\"mark_feature_usage\", {\n            detail: {\n                feature: \"next-third-parties-ga\"\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga-init\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n          window['${dataLayerName}'] = window['${dataLayerName}'] || [];\n          function gtag(){window['${dataLayerName}'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '${gaId}' ${debugMode ? \",{ 'debug_mode': true }\" : \"\"});`\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${gaId}`,\n                nonce: nonce\n            })\n        ]\n    });\n}\nfunction sendGAEvent(..._args) {\n    if (currDataLayerName === undefined) {\n        console.warn(`@next/third-parties: GA has not been initialized`);\n        return;\n    }\n    if (window[currDataLayerName]) {\n        window[currDataLayerName].push(arguments);\n    } else {\n        console.warn(`@next/third-parties: GA dataLayer ${currDataLayerName} does not exist`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/google/ga.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@next/third-parties/dist/google/gtm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/gtm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sendGTMEvent = void 0;\nexports.GoogleTagManager = GoogleTagManager;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = \"dataLayer\";\nfunction GoogleTagManager(props) {\n    const { gtmId, gtmScriptUrl = \"https://www.googletagmanager.com/gtm.js\", dataLayerName = \"dataLayer\", auth, preview, dataLayer, nonce } = props;\n    currDataLayerName = dataLayerName;\n    const gtmLayer = dataLayerName !== \"dataLayer\" ? `&l=${dataLayerName}` : \"\";\n    const gtmAuth = auth ? `&gtm_auth=${auth}` : \"\";\n    const gtmPreview = preview ? `&gtm_preview=${preview}&gtm_cookies_win=x` : \"\";\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark(\"mark_feature_usage\", {\n            detail: {\n                feature: \"next-third-parties-gtm\"\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm-init\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ${dataLayer ? `w[l].push(${JSON.stringify(dataLayer)})` : \"\"}\n      })(window,'${dataLayerName}');`\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm\",\n                \"data-ntpc\": \"GTM\",\n                src: `${gtmScriptUrl}?id=${gtmId}${gtmLayer}${gtmAuth}${gtmPreview}`,\n                nonce: nonce\n            })\n        ]\n    });\n}\nconst sendGTMEvent = (data, dataLayerName)=>{\n    // special case if we are sending events before GTM init and we have custom dataLayerName\n    const dataLayer = dataLayerName || currDataLayerName;\n    // define dataLayer so we can still queue up events before GTM init\n    window[dataLayer] = window[dataLayer] || [];\n    window[dataLayer].push(data);\n};\nexports.sendGTMEvent = sendGTMEvent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@next/third-parties/dist/google/gtm.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":
/*!************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("D:\\AI360\\Ai360\\AI360_App_V3\\frontend\\node_modules\\@next\\third-parties\\dist\\ThirdPartyScriptEmbed.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/ga.js":
/*!************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/ga.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("D:\\AI360\\Ai360\\AI360_App_V3\\frontend\\node_modules\\@next\\third-parties\\dist\\google\\ga.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/google-maps-embed.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = GoogleMapsEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nconst third_party_capital_1 = __webpack_require__(/*! third-party-capital */ \"(rsc)/./node_modules/third-party-capital/lib/cjs/index.js\");\nconst ThirdPartyScriptEmbed_1 = __importDefault(__webpack_require__(/*! ../ThirdPartyScriptEmbed */ \"(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\nfunction GoogleMapsEmbed(props) {\n    const { apiKey, ...restProps } = props;\n    const formattedProps = { ...restProps, key: apiKey };\n    const { html } = (0, third_party_capital_1.GoogleMapsEmbed)(formattedProps);\n    return ((0, jsx_runtime_1.jsx)(ThirdPartyScriptEmbed_1.default, { height: formattedProps.height || null, width: formattedProps.width || null, html: html, dataNtpc: \"GoogleMapsEmbed\" }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG5leHQvdGhpcmQtcGFydGllcy9kaXN0L2dvb2dsZS9nb29nbGUtbWFwcy1lbWJlZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGtCQUFlO0FBQ2Ysc0JBQXNCLG1CQUFPLENBQUMsZ0lBQW1CO0FBQ2pELDhCQUE4QixtQkFBTyxDQUFDLHNGQUFxQjtBQUMzRCxnREFBZ0QsbUJBQU8sQ0FBQyx3R0FBMEI7QUFDbEY7QUFDQSxZQUFZLHVCQUF1QjtBQUNuQyw2QkFBNkI7QUFDN0IsWUFBWSxPQUFPO0FBQ25CLHNFQUFzRSxxSEFBcUg7QUFDM0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haTM2MC8uL25vZGVfbW9kdWxlcy9AbmV4dC90aGlyZC1wYXJ0aWVzL2Rpc3QvZ29vZ2xlL2dvb2dsZS1tYXBzLWVtYmVkLmpzPzk5YzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHQgPSBHb29nbGVNYXBzRW1iZWQ7XG5jb25zdCBqc3hfcnVudGltZV8xID0gcmVxdWlyZShcInJlYWN0L2pzeC1ydW50aW1lXCIpO1xuY29uc3QgdGhpcmRfcGFydHlfY2FwaXRhbF8xID0gcmVxdWlyZShcInRoaXJkLXBhcnR5LWNhcGl0YWxcIik7XG5jb25zdCBUaGlyZFBhcnR5U2NyaXB0RW1iZWRfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi4vVGhpcmRQYXJ0eVNjcmlwdEVtYmVkXCIpKTtcbmZ1bmN0aW9uIEdvb2dsZU1hcHNFbWJlZChwcm9wcykge1xuICAgIGNvbnN0IHsgYXBpS2V5LCAuLi5yZXN0UHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGZvcm1hdHRlZFByb3BzID0geyAuLi5yZXN0UHJvcHMsIGtleTogYXBpS2V5IH07XG4gICAgY29uc3QgeyBodG1sIH0gPSAoMCwgdGhpcmRfcGFydHlfY2FwaXRhbF8xLkdvb2dsZU1hcHNFbWJlZCkoZm9ybWF0dGVkUHJvcHMpO1xuICAgIHJldHVybiAoKDAsIGpzeF9ydW50aW1lXzEuanN4KShUaGlyZFBhcnR5U2NyaXB0RW1iZWRfMS5kZWZhdWx0LCB7IGhlaWdodDogZm9ybWF0dGVkUHJvcHMuaGVpZ2h0IHx8IG51bGwsIHdpZHRoOiBmb3JtYXR0ZWRQcm9wcy53aWR0aCB8fCBudWxsLCBodG1sOiBodG1sLCBkYXRhTnRwYzogXCJHb29nbGVNYXBzRW1iZWRcIiB9KSk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/gtm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/gtm.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/* __next_internal_client_entry_do_not_use__  cjs */ 
const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");
module.exports = createProxy("D:\\AI360\\Ai360\\AI360_App_V3\\frontend\\node_modules\\@next\\third-parties\\dist\\google\\gtm.js");


/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/index.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sendGAEvent = exports.GoogleAnalytics = exports.sendGTMEvent = exports.GoogleTagManager = exports.YouTubeEmbed = exports.GoogleMapsEmbed = void 0;\nvar google_maps_embed_1 = __webpack_require__(/*! ./google-maps-embed */ \"(rsc)/./node_modules/@next/third-parties/dist/google/google-maps-embed.js\");\nObject.defineProperty(exports, \"GoogleMapsEmbed\", ({ enumerable: true, get: function () { return __importDefault(google_maps_embed_1).default; } }));\nvar youtube_embed_1 = __webpack_require__(/*! ./youtube-embed */ \"(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js\");\nObject.defineProperty(exports, \"YouTubeEmbed\", ({ enumerable: true, get: function () { return __importDefault(youtube_embed_1).default; } }));\nvar gtm_1 = __webpack_require__(/*! ./gtm */ \"(rsc)/./node_modules/@next/third-parties/dist/google/gtm.js\");\nObject.defineProperty(exports, \"GoogleTagManager\", ({ enumerable: true, get: function () { return gtm_1.GoogleTagManager; } }));\nObject.defineProperty(exports, \"sendGTMEvent\", ({ enumerable: true, get: function () { return gtm_1.sendGTMEvent; } }));\nvar ga_1 = __webpack_require__(/*! ./ga */ \"(rsc)/./node_modules/@next/third-parties/dist/google/ga.js\");\nObject.defineProperty(exports, \"GoogleAnalytics\", ({ enumerable: true, get: function () { return ga_1.GoogleAnalytics; } }));\nObject.defineProperty(exports, \"sendGAEvent\", ({ enumerable: true, get: function () { return ga_1.sendGAEvent; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/youtube-embed.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports[\"default\"] = YouTubeEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\"));\nconst third_party_capital_1 = __webpack_require__(/*! third-party-capital */ \"(rsc)/./node_modules/third-party-capital/lib/cjs/index.js\");\nconst ThirdPartyScriptEmbed_1 = __importDefault(__webpack_require__(/*! ../ThirdPartyScriptEmbed */ \"(rsc)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\nconst scriptStrategy = {\n    server: 'beforeInteractive',\n    client: 'afterInteractive',\n    idle: 'lazyOnload',\n    worker: 'worker',\n};\nfunction YouTubeEmbed(props) {\n    const { html, scripts, stylesheets } = (0, third_party_capital_1.YouTubeEmbed)(props);\n    return ((0, jsx_runtime_1.jsx)(ThirdPartyScriptEmbed_1.default, { height: props.height || null, width: props.width || null, html: html, dataNtpc: \"YouTubeEmbed\", children: scripts === null || scripts === void 0 ? void 0 : scripts.map((script) => ((0, jsx_runtime_1.jsx)(script_1.default, { src: script.url, strategy: scriptStrategy[script.strategy], stylesheets: stylesheets }, script.url))) }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@next/third-parties/dist/google/youtube-embed.js\n");

/***/ })

};
;