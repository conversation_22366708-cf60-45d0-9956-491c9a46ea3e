#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly.
"""

import requests
import json

def test_health_endpoint():
    """Test the health endpoint."""
    try:
        response = requests.get("http://localhost:8000/api/health")
        print(f"Health endpoint - Status: {response.status_code}")
        print(f"Health endpoint - Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health endpoint error: {e}")
        return False

def test_companies_get():
    """Test getting companies."""
    try:
        response = requests.get("http://localhost:8000/api/companies?user_id=test_user")
        print(f"Get companies - Status: {response.status_code}")
        print(f"Get companies - Response: {response.text}")
        return response.status_code in [200, 404]  # 404 is OK if no companies exist
    except Exception as e:
        print(f"Get companies error: {e}")
        return False

def test_companies_post():
    """Test creating a company."""
    try:
        data = {
            "name": "Test Company API",
            "description": "A test company created via API"
        }
        response = requests.post(
            "http://localhost:8000/api/companies?created_by=test_user",
            json=data
        )
        print(f"Create company - Status: {response.status_code}")
        print(f"Create company - Response: {response.text}")
        return response.status_code in [200, 201]
    except Exception as e:
        print(f"Create company error: {e}")
        return False

if __name__ == "__main__":
    print("Testing API endpoints...")
    print("=" * 50)
    
    # Test health endpoint
    health_ok = test_health_endpoint()
    print()
    
    # Test companies endpoints
    get_ok = test_companies_get()
    print()
    
    post_ok = test_companies_post()
    print()
    
    print("=" * 50)
    print(f"Health endpoint: {'✅' if health_ok else '❌'}")
    print(f"Get companies: {'✅' if get_ok else '❌'}")
    print(f"Create company: {'✅' if post_ok else '❌'}")
    
    if all([health_ok, get_ok, post_ok]):
        print("\n🎉 All API endpoints are working correctly!")
    else:
        print("\n⚠️ Some API endpoints have issues.")
