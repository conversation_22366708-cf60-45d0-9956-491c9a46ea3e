#!/usr/bin/env python3
"""
Check route registration in the FastAPI app.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def main():
    print("Checking route registration...")
    
    try:
        # Import the app
        from ai360.api.app import app
        print("App imported successfully")
        
        # Get all routes
        all_routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                if hasattr(route, 'methods'):
                    route_str = f"{list(route.methods)} {route.path}"
                else:
                    route_str = f"[MOUNT] {route.path}"
                all_routes.append(route_str)
        
        print(f"Total routes: {len(all_routes)}")
        
        # Find companies routes
        companies_routes = [r for r in all_routes if "/companies" in r]
        print(f"Companies routes: {len(companies_routes)}")
        
        if companies_routes:
            print("Companies routes found:")
            for route in companies_routes:
                print(f"  {route}")
        else:
            print("NO COMPANIES ROUTES FOUND!")
            
        # Find all /api routes
        api_routes = [r for r in all_routes if "/api" in r]
        print(f"\nAll /api routes ({len(api_routes)}):")
        for route in api_routes:
            print(f"  {route}")
            
        # Test companies router directly
        print("\nTesting companies router directly...")
        from ai360.api.routes.companies import router
        router_routes = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                router_routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"Companies router has {len(router_routes)} routes:")
        for route in router_routes:
            print(f"  {route}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
