#!/usr/bin/env python3
"""
Performance Testing for Multi-Company AI360 Platform

This script tests performance with multiple companies and large datasets:
- Company creation and retrieval performance
- Project management with large datasets
- User access control performance
- Database query optimization validation
- Memory usage and scalability testing
"""

import sys
import os
import tempfile
import time
import psutil
import sqlite3
from pathlib import Path
import uuid
import statistics

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from ai360.api.models.company import CompanyDatabase
    from ai360.api.models.project import ProjectDatabase
    from ai360.api.models.user_preferences import UserPreferencesDatabase
    print("✅ Successfully imported database models")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class PerformanceTester:
    """Performance testing for multi-company functionality."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.company_db_path = os.path.join(self.temp_dir, "perf_test_companies.db")
        self.project_db_path = os.path.join(self.temp_dir, "perf_test_projects.db")
        self.preferences_db_path = os.path.join(self.temp_dir, "perf_test_preferences.db")
        
        # Initialize databases
        self.company_db = CompanyDatabase(self.company_db_path)
        self.project_db = ProjectDatabase(self.project_db_path)
        self.preferences_db = UserPreferencesDatabase(self.preferences_db_path)
        
        # Performance metrics
        self.metrics = {
            'company_creation': [],
            'company_retrieval': [],
            'project_creation': [],
            'project_retrieval': [],
            'user_access_check': [],
            'memory_usage': []
        }
        
        print(f"⚡ Performance test environment initialized in: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        print("🧹 Performance test environment cleaned up")
    
    def measure_memory_usage(self):
        """Measure current memory usage."""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.metrics['memory_usage'].append(memory_mb)
        return memory_mb
    
    def time_operation(self, operation_name, func, *args, **kwargs):
        """Time an operation and record the result."""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        duration = end_time - start_time
        self.metrics[operation_name].append(duration)
        
        return result, duration
    
    def test_company_creation_performance(self):
        """Test 1: Company creation performance with large numbers."""
        print("\n🏢 Test 1: Company Creation Performance")
        print("-" * 50)
        
        num_companies = 1000
        print(f"Creating {num_companies} companies...")
        
        start_memory = self.measure_memory_usage()
        start_time = time.time()
        
        companies = []
        for i in range(num_companies):
            company_data = {
                "name": f"Performance Test Company {i:04d}",
                "description": f"Test company {i} for performance testing",
                "primary_color": "#3B82F6",
                "secondary_color": "#1E40AF"
            }
            
            company, duration = self.time_operation(
                'company_creation',
                self.company_db.create_company,
                company_data,
                f"perf_user_{i % 10}"  # Distribute across 10 users
            )
            companies.append(company)
            
            if (i + 1) % 100 == 0:
                print(f"✓ Created {i + 1} companies")
        
        total_time = time.time() - start_time
        end_memory = self.measure_memory_usage()
        
        avg_creation_time = statistics.mean(self.metrics['company_creation'])
        max_creation_time = max(self.metrics['company_creation'])
        min_creation_time = min(self.metrics['company_creation'])
        
        print(f"✓ Created {num_companies} companies in {total_time:.2f} seconds")
        print(f"✓ Average creation time: {avg_creation_time*1000:.2f}ms")
        print(f"✓ Min/Max creation time: {min_creation_time*1000:.2f}ms / {max_creation_time*1000:.2f}ms")
        print(f"✓ Memory usage: {start_memory:.1f}MB → {end_memory:.1f}MB (+{end_memory-start_memory:.1f}MB)")
        
        # Performance assertions
        assert avg_creation_time < 0.1, f"Company creation too slow: {avg_creation_time:.3f}s"
        assert max_creation_time < 0.5, f"Slowest company creation too slow: {max_creation_time:.3f}s"
        
        print("✅ Company Creation Performance: PASSED")
        return companies
    
    def test_company_retrieval_performance(self, companies):
        """Test 2: Company retrieval performance."""
        print("\n📊 Test 2: Company Retrieval Performance")
        print("-" * 50)
        
        # Test bulk retrieval
        start_time = time.time()
        all_companies, duration = self.time_operation(
            'company_retrieval',
            self.company_db.get_all_companies
        )
        
        print(f"✓ Retrieved {len(all_companies)} companies in {duration*1000:.2f}ms")
        
        # Test user-specific retrieval with many companies
        test_user = "perf_user_0"
        
        # Grant access to many companies
        print("Granting access to companies...")
        for i, company in enumerate(companies[:500]):  # Grant access to 500 companies
            self.company_db.grant_user_access(test_user, company['id'], "member", "system")
            if (i + 1) % 100 == 0:
                print(f"✓ Granted access to {i + 1} companies")
        
        # Test user company retrieval performance
        user_companies, duration = self.time_operation(
            'company_retrieval',
            self.company_db.get_companies_for_user,
            test_user
        )
        
        print(f"✓ Retrieved {len(user_companies)} user companies in {duration*1000:.2f}ms")
        
        # Performance assertions
        assert duration < 1.0, f"User company retrieval too slow: {duration:.3f}s"
        assert len(user_companies) == 500, f"Expected 500 companies, got {len(user_companies)}"
        
        print("✅ Company Retrieval Performance: PASSED")
    
    def test_project_management_performance(self, companies):
        """Test 3: Project management performance with large datasets."""
        print("\n📋 Test 3: Project Management Performance")
        print("-" * 50)
        
        num_projects_per_company = 100
        test_companies = companies[:10]  # Use first 10 companies
        
        print(f"Creating {num_projects_per_company} projects for {len(test_companies)} companies...")
        
        projects = []
        for company_idx, company in enumerate(test_companies):
            for project_idx in range(num_projects_per_company):
                project_data = {
                    "id": str(uuid.uuid4()),
                    "name": f"Project {project_idx:03d} for {company['name']}",
                    "description": f"Performance test project {project_idx}",
                    "user_id": f"perf_user_{company_idx}",
                    "company_id": company['id'],
                    "status": "Active",
                    "privacy": "Private",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "metadata": {"test_data": True}
                }
                
                project, duration = self.time_operation(
                    'project_creation',
                    self.project_db.create_project,
                    project_data
                )
                projects.append(project)
            
            print(f"✓ Created {num_projects_per_company} projects for company {company_idx + 1}")
        
        total_projects = len(projects)
        avg_creation_time = statistics.mean(self.metrics['project_creation'])
        
        print(f"✓ Created {total_projects} projects")
        print(f"✓ Average project creation time: {avg_creation_time*1000:.2f}ms")
        
        # Test project retrieval performance
        test_company = test_companies[0]
        test_user = "perf_user_0"
        
        user_projects, duration = self.time_operation(
            'project_retrieval',
            self.project_db.get_projects_by_user_and_company,
            test_user,
            test_company['id']
        )
        
        print(f"✓ Retrieved {len(user_projects)} projects in {duration*1000:.2f}ms")
        
        # Performance assertions
        assert avg_creation_time < 0.05, f"Project creation too slow: {avg_creation_time:.3f}s"
        assert duration < 0.1, f"Project retrieval too slow: {duration:.3f}s"
        assert len(user_projects) == num_projects_per_company
        
        print("✅ Project Management Performance: PASSED")
    
    def test_user_access_control_performance(self, companies):
        """Test 4: User access control performance."""
        print("\n🔐 Test 4: User Access Control Performance")
        print("-" * 50)
        
        num_users = 100
        test_companies = companies[:50]  # Use 50 companies
        
        print(f"Testing access control for {num_users} users across {len(test_companies)} companies...")
        
        # Grant various access levels
        for user_idx in range(num_users):
            user_id = f"access_test_user_{user_idx:03d}"
            
            # Grant access to random subset of companies
            import random
            user_companies = random.sample(test_companies, random.randint(1, 10))
            
            for company in user_companies:
                role = random.choice(["admin", "manager", "member", "viewer"])
                self.company_db.grant_user_access(user_id, company['id'], role, "system")
        
        print("✓ Set up access control for all users")
        
        # Test access checking performance
        access_check_times = []
        for user_idx in range(num_users):
            user_id = f"access_test_user_{user_idx:03d}"
            
            start_time = time.time()
            user_companies = self.company_db.get_companies_for_user(user_id)
            duration = time.time() - start_time
            
            access_check_times.append(duration)
            self.metrics['user_access_check'].append(duration)
        
        avg_access_time = statistics.mean(access_check_times)
        max_access_time = max(access_check_times)
        
        print(f"✓ Average access check time: {avg_access_time*1000:.2f}ms")
        print(f"✓ Maximum access check time: {max_access_time*1000:.2f}ms")
        
        # Performance assertions
        assert avg_access_time < 0.05, f"Access check too slow: {avg_access_time:.3f}s"
        assert max_access_time < 0.2, f"Slowest access check too slow: {max_access_time:.3f}s"
        
        print("✅ User Access Control Performance: PASSED")
    
    def test_database_optimization(self):
        """Test 5: Database optimization and indexing."""
        print("\n🗄️ Test 5: Database Optimization")
        print("-" * 50)
        
        # Check if indexes exist
        with sqlite3.connect(self.company_db_path) as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = [row[0] for row in cursor.fetchall()]
            
            expected_indexes = [
                'idx_companies_name',
                'idx_companies_parent',
                'idx_user_company_access_user_id',
                'idx_user_company_access_company_id'
            ]
            
            for expected_index in expected_indexes:
                if expected_index in indexes:
                    print(f"✓ Index exists: {expected_index}")
                else:
                    print(f"⚠️ Missing index: {expected_index}")
        
        print("✅ Database Optimization: CHECKED")
    
    def generate_performance_report(self):
        """Generate performance report."""
        print("\n📊 Performance Report")
        print("=" * 60)
        
        for operation, times in self.metrics.items():
            if times and operation != 'memory_usage':
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                
                print(f"{operation.replace('_', ' ').title()}:")
                print(f"  Average: {avg_time*1000:.2f}ms")
                print(f"  Min/Max: {min_time*1000:.2f}ms / {max_time*1000:.2f}ms")
                print(f"  Operations: {len(times)}")
                print()
        
        if self.metrics['memory_usage']:
            min_memory = min(self.metrics['memory_usage'])
            max_memory = max(self.metrics['memory_usage'])
            print(f"Memory Usage:")
            print(f"  Range: {min_memory:.1f}MB - {max_memory:.1f}MB")
            print(f"  Peak increase: {max_memory - min_memory:.1f}MB")
    
    def run_all_performance_tests(self):
        """Run all performance tests."""
        print("\n⚡ Starting Performance Tests")
        print("=" * 60)
        
        try:
            companies = self.test_company_creation_performance()
            self.test_company_retrieval_performance(companies)
            self.test_project_management_performance(companies)
            self.test_user_access_control_performance(companies)
            self.test_database_optimization()
            
            self.generate_performance_report()
            
            print("\n✅ All Performance Tests Passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Performance Test Failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()

def main():
    """Main performance test runner."""
    tester = PerformanceTester()
    success = tester.run_all_performance_tests()
    
    if success:
        print("\n⚡ Performance Testing: PASSED")
        sys.exit(0)
    else:
        print("\n⚠️ Performance Testing: FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
