"use client";

import React, { useContext, useEffect, useState } from "react";

import { SocketContext } from "../contexts/SocketContext";
import { useCompanies } from "../contexts/CompanyContext";
import { RouterContext } from "../contexts/RouterContext";

import { MdChatBubbleOutline } from "react-icons/md";
import { GoDatabase } from "react-icons/go";
import { AiOutlineExperiment } from "react-icons/ai";
import { FaCircle, FaSquareXTwitter } from "react-icons/fa6";
import { MdOutlineSettingsInputComponent } from "react-icons/md";
import { IoIosWarning } from "react-icons/io";
import { HiOutlineFolder, HiOutlineHome } from "react-icons/hi";
import { Building2, ChevronDown } from "lucide-react";

import HomeSubMenu from "@/app/components/navigation/HomeSubMenu";
import DataSubMenu from "@/app/components/navigation/DataSubMenu";
import EvalSubMenu from "@/app/components/navigation/EvalSubMenu";

import { CgFileDocument } from "react-icons/cg";

import { CgWebsite } from "react-icons/cg";
import { IoNewspaperOutline } from "react-icons/io5";
import { FaGithub } from "react-icons/fa";
import { FaLinkedin } from "react-icons/fa";
import { FaYoutube } from "react-icons/fa";

import { RiRobot2Line } from "react-icons/ri";

import { public_path } from "@/app/components/host";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenuItem,
  SidebarMenu,
  SidebarMenuButton,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar";

import { Separator } from "@/components/ui/separator";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import SettingsSubMenu from "./SettingsSubMenu";
import ProjectOverview from "./ProjectOverview";
import { CollectionContext } from "../contexts/CollectionContext";
import { SessionContext } from "../contexts/SessionContext";
import { useCustomization } from "../contexts/CustomizationContext";
import { useProjects } from "../contexts/ProjectContext";
import packageJson from "../../../package.json";

const SidebarComponent: React.FC = () => {
  const { socketOnline } = useContext(SocketContext);
  const { changePage, currentPage } = useContext(RouterContext);
  const { collections, loadingCollections } = useContext(CollectionContext);
  const { unsavedChanges } = useContext(SessionContext);
  const { settings } = useCustomization();
  const { currentCompany, companies, switchCompany } = useCompanies();
  const { currentProject } = useProjects();

  const [items, setItems] = useState<
    {
      title: string;
      mode: string[];
      icon: React.ReactNode;
      warning?: boolean;
      loading?: boolean;
      disabled?: boolean;
      onClick: () => void;
    }[]
  >([]);

  useEffect(() => {
    const _items = [
      {
        title: "Home",
        mode: ["home"],
        icon: <HiOutlineHome />,
        onClick: () => changePage("home", {}, true, unsavedChanges),
      },
      {
        title: "Projects",
        mode: ["projects", "project-detail"],
        icon: <HiOutlineFolder />,
        onClick: () => changePage("projects", {}, true, unsavedChanges),
      },
      {
        title: "Chat",
        mode: ["chat"],
        icon: <MdChatBubbleOutline />,
        disabled: !currentProject,
        onClick: () => {
          if (!currentProject) {
            // Show warning or redirect to projects
            changePage("projects", {}, true, unsavedChanges);
            return;
          }
          changePage("chat", {}, true, unsavedChanges);
        },
      },
      {
        title: "Data",
        mode: ["data", "collection"],
        icon: !collections?.some((c) => c.processed === true) ? (
          <IoIosWarning className="text-warning" />
        ) : (
          <GoDatabase />
        ),
        warning: !collections?.some((c) => c.processed === true),
        loading: loadingCollections,
        disabled: !currentProject,
        onClick: () => {
          if (!currentProject) {
            changePage("projects", {}, true, unsavedChanges);
            return;
          }
          changePage("data", {}, true, unsavedChanges);
        },
      },
      {
        title: "Settings",
        mode: ["settings", "ai360", "theme", "customization", "display"],
        icon: <MdOutlineSettingsInputComponent />,
        onClick: () => changePage("settings", {}, true, unsavedChanges),
      },
      {
        title: "Evaluation",
        mode: ["eval", "feedback"],
        icon: <AiOutlineExperiment />,
        onClick: () => changePage("eval", {}, true, unsavedChanges),
      },
    ];
    setItems(_items);
  }, [collections, unsavedChanges, currentProject]);

  const openNewTab = (url: string) => {
    window.open(url, "_blank");
  };

  return (
    <Sidebar className="fade-in">
      <SidebarHeader>
        <div className={`flex items-center gap-2 w-full justify-between p-2`}>
          <div className="flex items-center gap-2">
            <img
              src={`${public_path}logo.svg`}
              alt={settings.applicationName}
              className="w-5 h-5 stext-primary"
            />
            <p className="text-sm font-bold text-primary">{settings.sidebarTitle}</p>
          </div>
          <div className="flex items-center justify-center gap-1">
            {socketOnline ? (
              <FaCircle scale={0.2} className="text-lg pulsing_color w-5 h-5" />
            ) : (
              <FaCircle scale={0.2} className="text-lg pulsing w-5 h-5" />
            )}
            <div className="flex flex-col items-end">
              <p className="text-xs text-muted-foreground">
                v{packageJson.version}
              </p>
            </div>
          </div>
        </div>

        {/* Company Indicator */}
        {currentCompany && (
          <div className="px-2 pb-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center gap-2 p-2 rounded-md hover:bg-accent/10 cursor-pointer transition-colors">
                  <div
                    className="w-6 h-6 rounded flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: currentCompany.primary_color }}
                  >
                    {currentCompany.logo_url ? (
                      <img
                        src={currentCompany.logo_url}
                        alt={currentCompany.name}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      currentCompany.name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('')
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium text-primary truncate">
                      {currentCompany.name}
                    </p>
                    {currentCompany.role && (
                      <p className="text-xs text-muted-foreground capitalize">
                        {currentCompany.role}
                      </p>
                    )}
                  </div>
                  <ChevronDown className="w-3 h-3 text-muted-foreground" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-56">
                {companies.map((company) => (
                  <DropdownMenuItem
                    key={company.id}
                    onClick={() => switchCompany(company.id)}
                    className={`flex items-center gap-2 ${
                      currentCompany.id === company.id ? 'bg-accent/20' : ''
                    }`}
                  >
                    <div
                      className="w-5 h-5 rounded flex items-center justify-center text-white text-xs font-bold"
                      style={{ backgroundColor: company.primary_color }}
                    >
                      {company.logo_url ? (
                        <img
                          src={company.logo_url}
                          alt={company.name}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        company.name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('')
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{company.name}</p>
                      {company.role && (
                        <p className="text-xs text-muted-foreground capitalize">
                          {company.role}
                        </p>
                      )}
                    </div>
                    {currentCompany.id === company.id && (
                      <FaCircle className="w-2 h-2 text-accent" />
                    )}
                  </DropdownMenuItem>
                ))}
                <DropdownMenuItem
                  onClick={() => changePage("companies", {}, true)}
                  className="flex items-center gap-2 border-t mt-1 pt-1"
                >
                  <Building2 className="w-4 h-4" />
                  <span>Manage Companies</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    variant={
                      item.mode.includes(currentPage)
                        ? "active"
                        : item.warning
                          ? "warning"
                          : "default"
                    }
                    onClick={item.onClick}
                    disabled={item.disabled}
                  >
                    <p className={`flex items-center gap-2 ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
                      {item.loading ? (
                        <FaCircle
                          scale={0.2}
                          className="text-lg pulsing_color"
                        />
                      ) : item.warning ? (
                        <IoIosWarning className="text-warning" />
                      ) : (
                        item.icon
                      )}
                      <span>{item.title}</span>
                      {item.disabled && (
                        <span className="text-xs text-muted-foreground ml-auto">
                          Select Project
                        </span>
                      )}
                    </p>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator />

        {/* Project Overview - Show on all pages except home and projects */}
        {!["home", "projects", "project-detail"].includes(currentPage) && (
          <ProjectOverview />
        )}

        {currentPage === "chat" && <HomeSubMenu />}
        {(currentPage === "data" || currentPage === "collection") && (
          <DataSubMenu />
        )}
        {(currentPage === "eval" ||
          currentPage === "feedback" ||
          currentPage === "display") && <EvalSubMenu />}
        {(currentPage === "settings" ||
          currentPage === "ai360" ||
          currentPage === "theme" ||
          currentPage === "customization") && (
          <SettingsSubMenu />
        )}
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="w-full justify-start items-center"
              onClick={() => openNewTab("https://weaviate.github.io/ai360/")}
            >
              <CgFileDocument />
              <span>AI360 Documentation</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="w-full justify-start items-center"
              onClick={() => openNewTab("https://github.com/weaviate/ai360")}
            >
              <FaGithub />
              <span>GitHub</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton>
                  <img
                    src={`${public_path}weaviate-logo.svg`}
                    alt="AI360"
                    className="w-4 h-4"
                  />
                  <p>Powered by AI360</p>
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="top"
                className="w-[--radix-popper-anchor-width]"
              >
                <DropdownMenuItem
                  onClick={() => openNewTab("https://weaviate.io/")}
                >
                  <CgWebsite />
                  <span>Website</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    openNewTab("https://weaviate.io/product/query-agent")
                  }
                >
                  <RiRobot2Line />
                  <span>Weaviate Query Agent</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => openNewTab("https://newsletter.weaviate.io/")}
                >
                  <IoNewspaperOutline />
                  <span>Newsletter</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    openNewTab("https://github.com/weaviate/weaviate")
                  }
                >
                  <FaGithub />
                  <span>GitHub</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    openNewTab(
                      "https://www.linkedin.com/company/weaviate-io/posts/?feedView=all"
                    )
                  }
                >
                  <FaLinkedin />
                  <span>LinkedIn</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => openNewTab("https://x.com/weaviate_io")}
                >
                  <FaSquareXTwitter />
                  <span>X</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    openNewTab("https://www.youtube.com/@Weaviate")
                  }
                >
                  <FaYoutube />
                  <span>YouTube</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default SidebarComponent;
