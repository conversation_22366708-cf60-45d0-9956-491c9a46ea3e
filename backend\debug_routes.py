#!/usr/bin/env python3
"""
Debug the route registration issue.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def debug_app_import():
    """Debug the app import and route registration."""
    print("=== Debugging App Import ===")
    
    try:
        # Test companies router import first
        print("1. Testing companies router import...")
        from ai360.api.routes.companies import router as companies_router
        print(f"   Companies router imported successfully")
        print(f"   Companies router has {len(companies_router.routes)} routes")
        
        # Test app import
        print("2. Testing app import...")
        from ai360.api.app import app
        print(f"   App imported successfully")
        
        # Check all routes in app
        print("3. Checking app routes...")
        all_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                route_info = f"{list(route.methods)} {route.path}"
                all_routes.append(route_info)
                if '/companies' in route.path:
                    print(f"   FOUND COMPANIES ROUTE: {route_info}")
        
        print(f"   Total app routes: {len(all_routes)}")
        
        # Check if companies routes are in app
        companies_in_app = [r for r in all_routes if '/companies' in r]
        print(f"   Companies routes in app: {len(companies_in_app)}")
        
        if len(companies_in_app) == 0:
            print("   ERROR: No companies routes found in app!")
            print("   This suggests the router is not being included properly.")
        else:
            print("   SUCCESS: Companies routes are registered in app!")
            
        return len(companies_in_app) > 0
        
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_api_call():
    """Test calling the API directly through the app."""
    print("\n=== Testing Direct API Call ===")
    
    try:
        from ai360.api.app import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health endpoint
        print("1. Testing health endpoint...")
        response = client.get("/api/health")
        print(f"   Health status: {response.status_code}")
        
        # Test companies GET endpoint
        print("2. Testing companies GET endpoint...")
        response = client.get("/api/companies?user_id=test_user")
        print(f"   Companies GET status: {response.status_code}")
        print(f"   Companies GET response: {response.text}")
        
        # Test companies POST endpoint
        print("3. Testing companies POST endpoint...")
        response = client.post(
            "/api/companies?created_by=test_user",
            json={"name": "Test Company Direct", "description": "Direct test"}
        )
        print(f"   Companies POST status: {response.status_code}")
        print(f"   Companies POST response: {response.text}")
        
        return response.status_code in [200, 201]
        
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Route Registration Debug")
    print("=" * 50)
    
    routes_ok = debug_app_import()
    api_ok = test_direct_api_call()
    
    print("\n" + "=" * 50)
    print(f"Routes registered: {'YES' if routes_ok else 'NO'}")
    print(f"API working: {'YES' if api_ok else 'NO'}")
    
    if routes_ok and api_ok:
        print("\nSUCCESS: Everything is working!")
    elif routes_ok and not api_ok:
        print("\nISSUE: Routes are registered but API calls fail")
    elif not routes_ok and api_ok:
        print("\nWEIRD: API works but routes not found in app")
    else:
        print("\nPROBLEM: Both routes and API have issues")
