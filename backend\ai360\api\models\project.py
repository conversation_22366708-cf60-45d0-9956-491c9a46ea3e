"""
SQLite database models for project management.
"""

import sqlite3
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
import os

from ai360.api.core.log import logger


class ProjectDatabase:
    """SQLite database manager for projects."""
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize the database connection and create tables if they don't exist."""
        if db_path is None:
            # Default to a projects.db file in the backend directory
            backend_dir = Path(__file__).parent.parent.parent.parent
            db_path = backend_dir / "data" / "projects.db"
            
        # Ensure the data directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        self.db_path = str(db_path)
        self._init_database()
    
    def _init_database(self):
        """Create the projects table if it doesn't exist."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    client TEXT,
                    status TEXT NOT NULL,
                    privacy TEXT NOT NULL DEFAULT 'Private',
                    start_date TEXT,
                    deadline TEXT,
                    summary TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    company_id TEXT NOT NULL,
                    metadata TEXT DEFAULT '{}',
                    FOREIGN KEY (company_id) REFERENCES companies(id)
                )
            """)

            # Create index for faster user queries
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_user_id
                ON projects(user_id)
            """)

            # Create index for status queries
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_status
                ON projects(status)
            """)

            # Create index for company queries
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_company_id
                ON projects(company_id)
            """)

            # Create composite index for user-company queries
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_projects_user_company
                ON projects(user_id, company_id)
            """)

            conn.commit()
            logger.info(f"Initialized project database at {self.db_path}")
    
    def create_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new project."""
        now = datetime.utcnow().isoformat()
        
        # Ensure required fields
        if 'id' not in project_data:
            raise ValueError("Project ID is required")
        if 'name' not in project_data:
            raise ValueError("Project name is required")
        if 'user_id' not in project_data:
            raise ValueError("User ID is required")
        if 'company_id' not in project_data:
            raise ValueError("Company ID is required")
        
        # Set defaults
        project_data.setdefault('status', 'Preparing')
        project_data.setdefault('privacy', 'Private')
        project_data.setdefault('created_at', now)
        project_data.setdefault('updated_at', now)
        project_data.setdefault('metadata', {})
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO projects (
                    id, name, client, status, privacy, start_date, deadline,
                    summary, created_at, updated_at, user_id, company_id, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                project_data['id'],
                project_data['name'],
                project_data.get('client'),
                project_data['status'],
                project_data['privacy'],
                project_data.get('start_date'),
                project_data.get('deadline'),
                project_data.get('summary'),
                project_data['created_at'],
                project_data['updated_at'],
                project_data['user_id'],
                project_data['company_id'],
                json.dumps(project_data['metadata'])
            ))
            conn.commit()
        
        logger.info(f"Created project {project_data['id']} for user {project_data['user_id']}")

        # Return the created project with consistent data structure
        return self.get_project(project_data['id'], project_data['user_id'])
    
    def get_project(self, project_id: str, user_id: str, company_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a project by ID for a specific user and optionally company."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                if company_id:
                    cursor = conn.execute("""
                        SELECT * FROM projects
                        WHERE id = ? AND user_id = ? AND company_id = ?
                    """, (project_id, user_id, company_id))
                else:
                    cursor = conn.execute("""
                        SELECT * FROM projects
                        WHERE id = ? AND user_id = ?
                    """, (project_id, user_id))

                row = cursor.fetchone()
                if row:
                    project = dict(row)
                    project['metadata'] = json.loads(project['metadata'] or '{}')
                    logger.debug(f"Found project {project_id} for user {user_id}")
                    return project
                else:
                    logger.debug(f"Project {project_id} not found for user {user_id}")
                    return None
        except Exception as e:
            logger.error(f"Error getting project {project_id} for user {user_id}: {str(e)}")
            return None
    
    def get_projects_by_user(self, user_id: str, company_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all projects for a user, optionally filtered by company and status."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row

            # Build query based on filters
            base_query = "SELECT * FROM projects WHERE user_id = ?"
            params = [user_id]

            if company_id:
                base_query += " AND company_id = ?"
                params.append(company_id)

            if status:
                base_query += " AND status = ?"
                params.append(status)

            base_query += " ORDER BY updated_at DESC"

            cursor = conn.execute(base_query, params)

            projects = []
            for row in cursor.fetchall():
                project = dict(row)
                project['metadata'] = json.loads(project['metadata'] or '{}')
                projects.append(project)

            return projects

    def get_projects_by_user_and_company(self, user_id: str, company_id: str, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all projects for a user in a specific company."""
        return self.get_projects_by_user(user_id, company_id, status)
    
    def update_project(self, project_id: str, user_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a project."""
        # First check if project exists and belongs to user
        existing = self.get_project(project_id, user_id)
        if not existing:
            logger.warning(f"Attempted to update non-existent project {project_id} for user {user_id}")
            return None
        
        # Update timestamp
        updates['updated_at'] = datetime.utcnow().isoformat()
        
        # Handle metadata separately
        metadata = updates.pop('metadata', None)
        if metadata is not None:
            updates['metadata'] = json.dumps(metadata)
        
        # Build dynamic update query
        set_clauses = []
        values = []
        for key, value in updates.items():
            set_clauses.append(f"{key} = ?")
            values.append(value)
        
        if not set_clauses:
            return existing  # No updates to make
        
        values.extend([project_id, user_id])
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(f"""
                UPDATE projects 
                SET {', '.join(set_clauses)}
                WHERE id = ? AND user_id = ?
            """, values)
            conn.commit()
        
        logger.info(f"Updated project {project_id} for user {user_id}")
        return self.get_project(project_id, user_id)

    def list_all_projects(self) -> List[Dict[str, Any]]:
        """List all projects in the database (for debugging)."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT id, name, user_id, status FROM projects")

                projects = []
                for row in cursor.fetchall():
                    projects.append(dict(row))

                logger.debug(f"Found {len(projects)} total projects in database")
                return projects
        except Exception as e:
            logger.error(f"Error listing all projects: {str(e)}")
            return []
    
    def delete_project(self, project_id: str, user_id: str) -> bool:
        """Delete a project."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                DELETE FROM projects 
                WHERE id = ? AND user_id = ?
            """, (project_id, user_id))
            conn.commit()
            
            deleted = cursor.rowcount > 0
            if deleted:
                logger.info(f"Deleted project {project_id} for user {user_id}")
            return deleted
    
    def get_project_stats(self, user_id: str) -> Dict[str, Any]:
        """Get project statistics for a user."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'Won' THEN 1 END) as won,
                    COUNT(CASE WHEN status = 'Lost' THEN 1 END) as lost,
                    COUNT(CASE WHEN status IN ('Preparing', 'Writing', 'In Review') THEN 1 END) as active
                FROM projects 
                WHERE user_id = ?
            """, (user_id,))
            
            row = cursor.fetchone()
            return {
                'total': row[0],
                'won': row[1],
                'lost': row[2],
                'active': row[3]
            }


# Global database instance
_db_instance = None

def get_project_db() -> ProjectDatabase:
    """Get the global project database instance."""
    global _db_instance
    if _db_instance is None:
        _db_instance = ProjectDatabase()
    return _db_instance
