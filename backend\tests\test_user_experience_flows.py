#!/usr/bin/env python3
"""
User Experience Flow Test for Multi-Company AI360 Platform

This script validates the complete user journey:
- Company selection workflow
- Project management within company context
- Company switching scenarios
- Settings and preferences management
- Navigation flow validation
"""

import sys
import os
import tempfile
import json
from pathlib import Path
import uuid

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from ai360.api.models.company import CompanyDatabase
    from ai360.api.models.project import ProjectDatabase
    from ai360.api.models.user_preferences import UserPreferencesDatabase
    print("✅ Successfully imported database models")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class UserExperienceValidator:
    """User experience flow validation test runner."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.company_db_path = os.path.join(self.temp_dir, "ux_test_companies.db")
        self.project_db_path = os.path.join(self.temp_dir, "ux_test_projects.db")
        self.preferences_db_path = os.path.join(self.temp_dir, "ux_test_preferences.db")
        
        # Initialize databases
        self.company_db = CompanyDatabase(self.company_db_path)
        self.project_db = ProjectDatabase(self.project_db_path)
        self.preferences_db = UserPreferencesDatabase(self.preferences_db_path)
        
        # Test user
        self.test_user = "test_user_ux"
        self.companies = []
        self.current_company = None
        
        print(f"🎯 UX test environment initialized in: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        print("🧹 UX test environment cleaned up")
    
    def test_initial_user_journey(self):
        """Test 1: Initial user journey - first time user."""
        print("\n🚀 Test 1: Initial User Journey")
        print("-" * 40)
        
        # Step 1: User lands on home page (no companies yet)
        user_companies = self.company_db.get_companies_for_user(self.test_user)
        assert len(user_companies) == 0, "New user should have no companies"
        print("✓ New user has no companies initially")
        
        # Step 2: User creates their first company
        company_data = {
            "name": "My First Company",
            "description": "User's first company",
            "primary_color": "#3B82F6",
            "secondary_color": "#1E40AF"
        }
        
        first_company = self.company_db.create_company(company_data, self.test_user)
        self.companies.append(first_company)
        
        # Grant admin access to creator
        self.company_db.grant_user_access(self.test_user, first_company['id'], "admin", "system")
        print(f"✓ Created first company: {first_company['name']}")
        
        # Step 3: User should now see their company
        user_companies = self.company_db.get_companies_for_user(self.test_user)
        assert len(user_companies) == 1, "User should have one company"
        assert user_companies[0]['name'] == "My First Company"
        print("✓ User can see their created company")
        
        # Step 4: User selects company and proceeds to projects
        self.current_company = first_company
        print(f"✓ User selected company: {self.current_company['name']}")
        
        print("✅ Initial User Journey: PASSED")
    
    def test_project_management_workflow(self):
        """Test 2: Project management within company context."""
        print("\n📋 Test 2: Project Management Workflow")
        print("-" * 40)
        
        # Step 1: User creates a project in their company
        project_data = {
            "id": str(uuid.uuid4()),
            "name": "My First Project",
            "description": "User's first project",
            "user_id": self.test_user,
            "company_id": self.current_company['id'],
            "status": "Active",
            "privacy": "Private",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "metadata": {}
        }
        
        project = self.project_db.create_project(project_data)
        print(f"✓ Created project: {project['name']}")
        
        # Step 2: User views their projects (should see the created project)
        user_projects = self.project_db.get_projects_by_user_and_company(
            self.test_user, self.current_company['id']
        )
        assert len(user_projects) == 1, "User should see their project"
        assert user_projects[0]['name'] == "My First Project"
        print("✓ User can view their projects")
        
        # Step 3: User updates the project
        updated_project = self.project_db.update_project(
            project['id'], 
            {"description": "Updated project description"}, 
            self.test_user
        )
        assert updated_project['description'] == "Updated project description"
        print("✓ User can update their project")
        
        # Step 4: User creates another project
        project_data_2 = {
            "id": str(uuid.uuid4()),
            "name": "Second Project",
            "description": "User's second project",
            "user_id": self.test_user,
            "company_id": self.current_company['id'],
            "status": "Planning",
            "privacy": "Private",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "metadata": {}
        }
        
        project_2 = self.project_db.create_project(project_data_2)
        print(f"✓ Created second project: {project_2['name']}")
        
        # Step 5: User should now see both projects
        user_projects = self.project_db.get_projects_by_user_and_company(
            self.test_user, self.current_company['id']
        )
        assert len(user_projects) == 2, "User should see both projects"
        print("✓ User can see all their projects")
        
        print("✅ Project Management Workflow: PASSED")
    
    def test_multi_company_workflow(self):
        """Test 3: Multi-company workflow and switching."""
        print("\n🏢 Test 3: Multi-Company Workflow")
        print("-" * 40)
        
        # Step 1: User creates a second company
        company_data_2 = {
            "name": "Second Company",
            "description": "User's second company",
            "primary_color": "#10B981",
            "secondary_color": "#059669"
        }
        
        second_company = self.company_db.create_company(company_data_2, self.test_user)
        self.companies.append(second_company)
        
        # Grant admin access
        self.company_db.grant_user_access(self.test_user, second_company['id'], "admin", "system")
        print(f"✓ Created second company: {second_company['name']}")
        
        # Step 2: User should see both companies
        user_companies = self.company_db.get_companies_for_user(self.test_user)
        assert len(user_companies) == 2, "User should have two companies"
        company_names = [c['name'] for c in user_companies]
        assert "My First Company" in company_names
        assert "Second Company" in company_names
        print("✓ User can see both companies")
        
        # Step 3: User switches to second company
        self.current_company = second_company
        print(f"✓ User switched to: {self.current_company['name']}")
        
        # Step 4: User creates project in second company
        project_data_3 = {
            "id": str(uuid.uuid4()),
            "name": "Project in Second Company",
            "description": "Project for the second company",
            "user_id": self.test_user,
            "company_id": self.current_company['id'],
            "status": "Active",
            "privacy": "Private",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "metadata": {}
        }
        
        project_3 = self.project_db.create_project(project_data_3)
        print(f"✓ Created project in second company: {project_3['name']}")
        
        # Step 5: Verify data isolation - should only see projects for current company
        current_company_projects = self.project_db.get_projects_by_user_and_company(
            self.test_user, self.current_company['id']
        )
        assert len(current_company_projects) == 1, "Should see only one project in second company"
        assert current_company_projects[0]['name'] == "Project in Second Company"
        print("✓ Data isolation working - only sees current company projects")
        
        # Step 6: Switch back to first company
        self.current_company = self.companies[0]  # First company
        first_company_projects = self.project_db.get_projects_by_user_and_company(
            self.test_user, self.current_company['id']
        )
        assert len(first_company_projects) == 2, "Should see two projects in first company"
        print("✓ Switched back to first company - sees original projects")
        
        print("✅ Multi-Company Workflow: PASSED")
    
    def test_company_settings_management(self):
        """Test 4: Company settings and preferences management."""
        print("\n⚙️ Test 4: Company Settings Management")
        print("-" * 40)
        
        # Step 1: User updates company information
        update_data = {
            "description": "Updated description for my first company",
            "primary_color": "#EF4444",
            "secondary_color": "#DC2626"
        }
        
        updated_company = self.company_db.update_company(
            self.companies[0]['id'], 
            update_data, 
            self.test_user
        )
        
        assert updated_company['description'] == update_data['description']
        assert updated_company['primary_color'] == update_data['primary_color']
        print("✓ User can update company settings")
        
        # Step 2: User manages company hierarchy (create subsidiary)
        subsidiary_data = {
            "name": "Subsidiary Company",
            "description": "A subsidiary of the first company",
            "parent_company_id": self.companies[0]['id'],
            "primary_color": "#EF4444",
            "secondary_color": "#DC2626"
        }
        
        subsidiary = self.company_db.create_company(subsidiary_data, self.test_user)
        self.company_db.grant_user_access(self.test_user, subsidiary['id'], "admin", "system")
        print(f"✓ Created subsidiary: {subsidiary['name']}")
        
        # Step 3: Verify hierarchy
        subsidiaries = self.company_db.get_subsidiaries(self.companies[0]['id'])
        assert len(subsidiaries) == 1, "Should have one subsidiary"
        assert subsidiaries[0]['name'] == "Subsidiary Company"
        print("✓ Company hierarchy working correctly")
        
        print("✅ Company Settings Management: PASSED")
    
    def run_all_ux_tests(self):
        """Run all user experience validation tests."""
        print("\n🎯 Starting User Experience Flow Tests")
        print("=" * 60)
        
        try:
            self.test_initial_user_journey()
            self.test_project_management_workflow()
            self.test_multi_company_workflow()
            self.test_company_settings_management()
            
            print("\n✅ All User Experience Tests Passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ UX Test Failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()

def main():
    """Main UX validation runner."""
    validator = UserExperienceValidator()
    success = validator.run_all_ux_tests()
    
    if success:
        print("\n🎯 User Experience Validation: PASSED")
        sys.exit(0)
    else:
        print("\n⚠️ User Experience Validation: FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
