#!/usr/bin/env python3
"""
Security Validation Test for Multi-Company AI360 Platform

This script validates security boundaries and access controls:
- Cross-company data access prevention
- Role-based permission enforcement
- API endpoint security validation
- Session security and company context validation
"""

import sys
import os
import tempfile
import sqlite3
import json
from pathlib import Path
import uuid

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from ai360.api.models.company import CompanyDatabase
    from ai360.api.models.project import ProjectDatabase
    from ai360.api.models.user_preferences import UserPreferencesDatabase
    print("✅ Successfully imported database models")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

class SecurityValidator:
    """Security validation test runner."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.company_db_path = os.path.join(self.temp_dir, "security_test_companies.db")
        self.project_db_path = os.path.join(self.temp_dir, "security_test_projects.db")
        self.preferences_db_path = os.path.join(self.temp_dir, "security_test_preferences.db")
        
        # Initialize databases
        self.company_db = CompanyDatabase(self.company_db_path)
        self.project_db = ProjectDatabase(self.project_db_path)
        self.preferences_db = UserPreferencesDatabase(self.preferences_db_path)
        
        # Test data
        self.companies = []
        self.projects = []
        self.users = ["alice", "bob", "charlie", "unauthorized_user"]
        
        print(f"🔒 Security test environment initialized in: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        print("🧹 Security test environment cleaned up")
    
    def setup_test_data(self):
        """Set up test companies, users, and projects."""
        print("\n📋 Setting up test data...")
        
        # Create test companies
        companies_data = [
            {
                "name": "Company Alpha",
                "description": "Confidential company Alpha",
                "primary_color": "#FF0000",
                "secondary_color": "#AA0000"
            },
            {
                "name": "Company Beta", 
                "description": "Secret company Beta",
                "primary_color": "#00FF00",
                "secondary_color": "#00AA00"
            },
            {
                "name": "Company Gamma",
                "description": "Private company Gamma",
                "primary_color": "#0000FF",
                "secondary_color": "#0000AA"
            }
        ]
        
        for company_data in companies_data:
            company = self.company_db.create_company(company_data, "system")
            self.companies.append(company)
            print(f"✓ Created company: {company['name']}")
        
        # Set up user access permissions
        access_matrix = [
            ("alice", self.companies[0]['id'], "admin"),    # Alice: Admin of Alpha
            ("alice", self.companies[1]['id'], "member"),   # Alice: Member of Beta
            ("bob", self.companies[1]['id'], "admin"),      # Bob: Admin of Beta
            ("bob", self.companies[2]['id'], "viewer"),     # Bob: Viewer of Gamma
            ("charlie", self.companies[2]['id'], "admin"),  # Charlie: Admin of Gamma
            # unauthorized_user has no access to any company
        ]
        
        for user_id, company_id, role in access_matrix:
            success = self.company_db.grant_user_access(user_id, company_id, role, "system")
            print(f"✓ Granted {role} access to {user_id} for company {company_id}")
        
        # Create test projects with sensitive data
        projects_data = [
            {
                "id": str(uuid.uuid4()),
                "name": "Alpha Secret Project",
                "description": "Highly confidential project for Alpha",
                "user_id": "alice",
                "company_id": self.companies[0]['id'],
                "status": "Active",
                "privacy": "Private",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "metadata": {"classification": "top_secret"}
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Beta Internal Project",
                "description": "Internal project for Beta",
                "user_id": "bob",
                "company_id": self.companies[1]['id'],
                "status": "Active",
                "privacy": "Private",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "metadata": {"classification": "internal"}
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Gamma Confidential Project",
                "description": "Confidential project for Gamma",
                "user_id": "charlie",
                "company_id": self.companies[2]['id'],
                "status": "Active",
                "privacy": "Private",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "metadata": {"classification": "confidential"}
            }
        ]
        
        for project_data in projects_data:
            project = self.project_db.create_project(project_data)
            self.projects.append(project)
            print(f"✓ Created project: {project['name']} for company {project['company_id']}")
        
        print("✅ Test data setup complete")
    
    def test_cross_company_access_prevention(self):
        """Test 1: Prevent cross-company data access."""
        print("\n🚫 Test 1: Cross-Company Access Prevention")
        print("-" * 50)
        
        # Test 1a: Alice should not see Bob's Beta projects (she's only a member)
        alice_beta_projects = self.project_db.get_projects_by_user_and_company("alice", self.companies[1]['id'])
        assert len(alice_beta_projects) == 0, "Alice should not see Bob's projects in Beta company"
        print("✓ Alice correctly blocked from seeing Bob's Beta projects")
        
        # Test 1b: Bob should not see Alice's Alpha projects
        bob_alpha_projects = self.project_db.get_projects_by_user_and_company("bob", self.companies[0]['id'])
        assert len(bob_alpha_projects) == 0, "Bob should not see Alice's Alpha projects"
        print("✓ Bob correctly blocked from seeing Alice's Alpha projects")
        
        # Test 1c: Charlie should not see any projects from Alpha or Beta
        charlie_alpha_projects = self.project_db.get_projects_by_user_and_company("charlie", self.companies[0]['id'])
        charlie_beta_projects = self.project_db.get_projects_by_user_and_company("charlie", self.companies[1]['id'])
        assert len(charlie_alpha_projects) == 0, "Charlie should not see Alpha projects"
        assert len(charlie_beta_projects) == 0, "Charlie should not see Beta projects"
        print("✓ Charlie correctly blocked from seeing Alpha and Beta projects")
        
        # Test 1d: Unauthorized user should see nothing
        unauthorized_projects_alpha = self.project_db.get_projects_by_user_and_company("unauthorized_user", self.companies[0]['id'])
        unauthorized_projects_beta = self.project_db.get_projects_by_user_and_company("unauthorized_user", self.companies[1]['id'])
        unauthorized_projects_gamma = self.project_db.get_projects_by_user_and_company("unauthorized_user", self.companies[2]['id'])
        
        assert len(unauthorized_projects_alpha) == 0, "Unauthorized user should not see Alpha projects"
        assert len(unauthorized_projects_beta) == 0, "Unauthorized user should not see Beta projects"
        assert len(unauthorized_projects_gamma) == 0, "Unauthorized user should not see Gamma projects"
        print("✓ Unauthorized user correctly blocked from all projects")
        
        print("✅ Cross-Company Access Prevention: PASSED")
    
    def test_role_based_permissions(self):
        """Test 2: Role-based permission enforcement."""
        print("\n👤 Test 2: Role-Based Permission Enforcement")
        print("-" * 50)
        
        # Test admin permissions
        alice_role_alpha = self.company_db.get_user_role("alice", self.companies[0]['id'])
        assert alice_role_alpha == "admin", f"Alice should be admin of Alpha, got {alice_role_alpha}"
        print("✓ Alice has admin role for Alpha company")
        
        # Test member permissions
        alice_role_beta = self.company_db.get_user_role("alice", self.companies[1]['id'])
        assert alice_role_beta == "member", f"Alice should be member of Beta, got {alice_role_beta}"
        print("✓ Alice has member role for Beta company")
        
        # Test viewer permissions
        bob_role_gamma = self.company_db.get_user_role("bob", self.companies[2]['id'])
        assert bob_role_gamma == "viewer", f"Bob should be viewer of Gamma, got {bob_role_gamma}"
        print("✓ Bob has viewer role for Gamma company")
        
        # Test no permissions
        unauthorized_role = self.company_db.get_user_role("unauthorized_user", self.companies[0]['id'])
        assert unauthorized_role is None, "Unauthorized user should have no role"
        print("✓ Unauthorized user has no role")
        
        print("✅ Role-Based Permission Enforcement: PASSED")
    
    def test_company_access_validation(self):
        """Test 3: Company access validation."""
        print("\n🏢 Test 3: Company Access Validation")
        print("-" * 50)
        
        # Test valid company access
        alice_companies = self.company_db.get_companies_for_user("alice")
        alice_company_ids = [c['id'] for c in alice_companies]
        
        assert self.companies[0]['id'] in alice_company_ids, "Alice should have access to Alpha"
        assert self.companies[1]['id'] in alice_company_ids, "Alice should have access to Beta"
        assert self.companies[2]['id'] not in alice_company_ids, "Alice should not have access to Gamma"
        print("✓ Alice's company access correctly validated")
        
        # Test unauthorized access
        unauthorized_companies = self.company_db.get_companies_for_user("unauthorized_user")
        assert len(unauthorized_companies) == 0, "Unauthorized user should have no company access"
        print("✓ Unauthorized user correctly has no company access")
        
        print("✅ Company Access Validation: PASSED")
    
    def run_all_security_tests(self):
        """Run all security validation tests."""
        print("\n🔒 Starting Security Validation Tests")
        print("=" * 60)
        
        try:
            self.setup_test_data()
            self.test_cross_company_access_prevention()
            self.test_role_based_permissions()
            self.test_company_access_validation()
            
            print("\n✅ All Security Validation Tests Passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Security Test Failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()

def main():
    """Main security validation runner."""
    validator = SecurityValidator()
    success = validator.run_all_security_tests()
    
    if success:
        print("\n🛡️ Security Validation: PASSED")
        sys.exit(0)
    else:
        print("\n⚠️ Security Validation: FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
