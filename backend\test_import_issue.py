#!/usr/bin/env python3
"""
Test to identify the import issue with companies router.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_step_by_step():
    """Test imports step by step to find the issue."""
    
    print("=== Step-by-step Import Test ===")
    
    try:
        print("1. Testing basic imports...")
        import ai360
        print("   ✓ ai360 imported")
        
        import ai360.api
        print("   ✓ ai360.api imported")
        
        import ai360.api.routes
        print("   ✓ ai360.api.routes imported")
        
        print("2. Testing individual route imports...")
        import ai360.api.routes.collections
        print("   ✓ collections imported")
        
        import ai360.api.routes.companies
        print("   ✓ companies imported")
        
        print("3. Testing companies router...")
        from ai360.api.routes.companies import router
        print(f"   ✓ companies router imported: {router}")
        print(f"   ✓ companies router has {len(router.routes)} routes")
        
        print("4. Testing app import...")
        from ai360.api.app import app
        print("   ✓ app imported")
        
        print("5. Checking app routes...")
        total_routes = len(app.routes)
        print(f"   ✓ app has {total_routes} total routes")
        
        # Check for companies routes in app
        companies_routes = []
        for route in app.routes:
            if hasattr(route, 'path') and '/companies' in route.path:
                if hasattr(route, 'methods'):
                    companies_routes.append(f"{list(route.methods)} {route.path}")
                else:
                    companies_routes.append(f"[MOUNT] {route.path}")
        
        print(f"   Companies routes in app: {len(companies_routes)}")
        for route in companies_routes:
            print(f"     {route}")
        
        if len(companies_routes) == 0:
            print("   ❌ NO COMPANIES ROUTES FOUND IN APP!")
            
            # Check what routes ARE in the app
            print("   Available /api routes:")
            for route in app.routes:
                if hasattr(route, 'path') and '/api' in route.path:
                    if hasattr(route, 'methods'):
                        print(f"     {list(route.methods)} {route.path}")
                    else:
                        print(f"     [MOUNT] {route.path}")
        else:
            print("   ✓ Companies routes found in app!")
            
        return len(companies_routes) > 0
        
    except Exception as e:
        print(f"❌ Error during import test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_router_registration():
    """Test if we can manually register the router."""
    
    print("\n=== Manual Router Registration Test ===")
    
    try:
        from fastapi import FastAPI
        from ai360.api.routes.companies import router
        
        # Create a test app
        test_app = FastAPI()
        test_app.include_router(router, prefix="/api", tags=["companies"])
        
        print("✓ Test app created with companies router")
        
        # Check routes
        companies_routes = []
        for route in test_app.routes:
            if hasattr(route, 'path') and '/companies' in route.path:
                if hasattr(route, 'methods'):
                    companies_routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"✓ Test app has {len(companies_routes)} companies routes:")
        for route in companies_routes:
            print(f"  {route}")
            
        return len(companies_routes) > 0
        
    except Exception as e:
        print(f"❌ Manual registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Import Issue Diagnosis")
    print("=" * 50)
    
    step_test = test_step_by_step()
    manual_test = test_direct_router_registration()
    
    print("\n" + "=" * 50)
    print("RESULTS:")
    print(f"Step-by-step test: {'PASS' if step_test else 'FAIL'}")
    print(f"Manual registration: {'PASS' if manual_test else 'FAIL'}")
    
    if not step_test and manual_test:
        print("\nDIAGNOSIS: Router works but not included in main app")
        print("SOLUTION: Check app.py router inclusion")
    elif step_test and manual_test:
        print("\nDIAGNOSIS: Everything works - server restart needed")
        print("SOLUTION: Restart the FastAPI server")
    else:
        print("\nDIAGNOSIS: Router has issues")
        print("SOLUTION: Check router definition and imports")
