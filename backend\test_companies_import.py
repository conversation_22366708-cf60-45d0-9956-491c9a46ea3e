#!/usr/bin/env python3
"""
Test companies module import to identify issues.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_companies_import():
    """Test importing the companies module."""
    print("Testing companies module import...")
    
    try:
        print("1. Importing companies module...")
        from ai360.api.routes import companies
        print("   ✓ Companies module imported successfully")
        
        print("2. Checking router...")
        router = companies.router
        print(f"   ✓ Router found: {router}")
        
        print("3. Checking routes...")
        routes = list(router.routes)
        print(f"   ✓ Router has {len(routes)} routes")
        
        for i, route in enumerate(routes):
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                print(f"   Route {i+1}: {list(route.methods)} {route.path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_with_companies():
    """Test creating an app with companies router."""
    print("\nTesting app creation with companies router...")
    
    try:
        from fastapi import FastAPI
        from ai360.api.routes.companies import router
        
        # Create test app
        test_app = FastAPI()
        test_app.include_router(router, prefix="/api", tags=["companies"])
        
        print("   ✓ Test app created with companies router")
        
        # Check routes
        companies_routes = []
        for route in test_app.routes:
            if hasattr(route, 'path') and '/companies' in route.path:
                if hasattr(route, 'methods'):
                    companies_routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"   ✓ Test app has {len(companies_routes)} companies routes")
        for route in companies_routes:
            print(f"     {route}")
        
        return len(companies_routes) > 0
        
    except Exception as e:
        print(f"   ❌ Test app creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_import():
    """Test importing the main app."""
    print("\nTesting main app import...")
    
    try:
        print("1. Importing main app...")
        from ai360.api.app import app
        print("   ✓ Main app imported successfully")
        
        print("2. Checking app routes...")
        total_routes = len(app.routes)
        print(f"   ✓ App has {total_routes} total routes")
        
        # Look for companies routes
        companies_routes = []
        api_routes = []
        
        for route in app.routes:
            if hasattr(route, 'path'):
                if '/companies' in route.path:
                    if hasattr(route, 'methods'):
                        companies_routes.append(f"{list(route.methods)} {route.path}")
                elif '/api' in route.path:
                    if hasattr(route, 'methods'):
                        api_routes.append(f"{list(route.methods)} {route.path}")
        
        print(f"   Companies routes in main app: {len(companies_routes)}")
        for route in companies_routes:
            print(f"     {route}")
        
        if len(companies_routes) == 0:
            print("   ❌ NO COMPANIES ROUTES FOUND!")
            print(f"   Available /api routes ({len(api_routes)}):")
            for route in api_routes[:5]:  # Show first 5
                print(f"     {route}")
            if len(api_routes) > 5:
                print(f"     ... and {len(api_routes) - 5} more")
        
        return len(companies_routes) > 0
        
    except Exception as e:
        print(f"   ❌ Main app import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Companies Import Test")
    print("=" * 50)
    
    import_ok = test_companies_import()
    test_app_ok = test_app_with_companies()
    main_app_ok = test_main_app_import()
    
    print("\n" + "=" * 50)
    print("RESULTS:")
    print(f"Companies import: {'PASS' if import_ok else 'FAIL'}")
    print(f"Test app creation: {'PASS' if test_app_ok else 'FAIL'}")
    print(f"Main app import: {'PASS' if main_app_ok else 'FAIL'}")
    
    if import_ok and test_app_ok and not main_app_ok:
        print("\nDIAGNOSIS: Companies router works but not included in main app")
        print("SOLUTION: Check main app router inclusion or restart server")
    elif import_ok and test_app_ok and main_app_ok:
        print("\nDIAGNOSIS: Everything works - server cache issue")
        print("SOLUTION: Restart FastAPI server")
    else:
        print("\nDIAGNOSIS: Companies router has import issues")
        print("SOLUTION: Fix import errors")
