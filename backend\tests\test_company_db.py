#!/usr/bin/env python3
"""
Test script to verify company database initialization and basic operations.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from ai360.api.models.company import CompanyDatabase, get_company_db

def test_database_initialization():
    """Test that the database initializes correctly."""
    print("Testing database initialization...")
    
    try:
        # Get database instance
        db = get_company_db()
        print(f"✅ Database initialized at: {db.db_path}")
        
        # Check if database file exists
        if os.path.exists(db.db_path):
            print(f"✅ Database file exists: {db.db_path}")
        else:
            print(f"❌ Database file does not exist: {db.db_path}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def test_create_company():
    """Test creating a company."""
    print("\nTesting company creation...")
    
    try:
        db = get_company_db()
        
        # Test company data
        company_data = {
            "id": "test-company-123",
            "name": "Test Company DB",
            "description": "A test company for database testing",
            "created_by": "test_user"
        }
        
        # Create company
        result = db.create_company(company_data)
        print(f"✅ Company created: {result['name']}")
        
        return True
    except Exception as e:
        print(f"❌ Company creation failed: {e}")
        return False

def test_get_companies():
    """Test getting companies for a user."""
    print("\nTesting get companies...")
    
    try:
        db = get_company_db()
        
        # Get companies for test user
        companies = db.get_companies_for_user("test_user")
        print(f"✅ Found {len(companies)} companies for test_user")
        
        for company in companies:
            print(f"  - {company['name']} (ID: {company['id']})")
        
        return True
    except Exception as e:
        print(f"❌ Get companies failed: {e}")
        return False

if __name__ == "__main__":
    print("Company Database Test")
    print("=" * 50)
    
    # Test database initialization
    init_ok = test_database_initialization()
    
    if init_ok:
        # Test company creation
        create_ok = test_create_company()
        
        # Test getting companies
        get_ok = test_get_companies()
        
        print("\n" + "=" * 50)
        print(f"Database initialization: {'✅' if init_ok else '❌'}")
        print(f"Company creation: {'✅' if create_ok else '❌'}")
        print(f"Get companies: {'✅' if get_ok else '❌'}")
        
        if all([init_ok, create_ok, get_ok]):
            print("\n🎉 All database tests passed!")
        else:
            print("\n⚠️ Some database tests failed.")
    else:
        print("\n❌ Database initialization failed. Cannot proceed with other tests.")
