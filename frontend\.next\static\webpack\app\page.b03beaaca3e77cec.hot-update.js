"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/pages/CompaniesPage.tsx":
/*!*************************************!*\
  !*** ./app/pages/CompaniesPage.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompaniesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./components/ui/alert-dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Building2,Crown,Edit,Eye,Loader2,Plus,Shield,Trash2,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_contexts_CompanyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/contexts/CompanyContext */ \"(app-pages-browser)/./app/components/contexts/CompanyContext.tsx\");\n/* harmony import */ var _components_contexts_RouterContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/contexts/RouterContext */ \"(app-pages-browser)/./app/components/contexts/RouterContext.tsx\");\n/* harmony import */ var _types_company__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../types/company */ \"(app-pages-browser)/./app/types/company.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CompaniesPage() {\n    _s();\n    const { companies, currentCompany, setCurrentCompany, createCompany, updateCompany, deleteCompany, switchCompany, loading, error } = (0,_components_contexts_CompanyContext__WEBPACK_IMPORTED_MODULE_11__.useCompanies)();\n    const { changePage } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_contexts_RouterContext__WEBPACK_IMPORTED_MODULE_12__.RouterContext);\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCompany, setEditingCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newCompanyData, setNewCompanyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        primary_color: \"#3B82F6\",\n        secondary_color: \"#1E40AF\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    };\n    const handleCreateCompany = async ()=>{\n        if (newCompanyData.name.trim()) {\n            try {\n                await createCompany(newCompanyData);\n                setNewCompanyData({\n                    name: \"\",\n                    description: \"\",\n                    primary_color: \"#3B82F6\",\n                    secondary_color: \"#1E40AF\"\n                });\n                setIsCreateModalOpen(false);\n            } catch (err) {\n                console.error(\"Failed to create company:\", err);\n            }\n        }\n    };\n    const handleEditCompany = async ()=>{\n        if (editingCompany && editingCompany.name.trim()) {\n            try {\n                const updateData = {\n                    name: editingCompany.name,\n                    description: editingCompany.description,\n                    logo_url: editingCompany.logo_url,\n                    primary_color: editingCompany.primary_color,\n                    secondary_color: editingCompany.secondary_color,\n                    parent_company_id: editingCompany.parent_company_id\n                };\n                await updateCompany(editingCompany.id, updateData);\n                setEditingCompany(null);\n                setIsEditModalOpen(false);\n            } catch (err) {\n                console.error(\"Failed to update company:\", err);\n            }\n        }\n    };\n    const handleDeleteCompany = async (companyId)=>{\n        try {\n            await deleteCompany(companyId);\n            // If we deleted the current company, clear it\n            if ((currentCompany === null || currentCompany === void 0 ? void 0 : currentCompany.id) === companyId) {\n                setCurrentCompany(null);\n            }\n        } catch (err) {\n            console.error(\"Failed to delete company:\", err);\n        }\n    };\n    const openEditModal = (company)=>{\n        setEditingCompany({\n            ...company\n        });\n        setIsEditModalOpen(true);\n    };\n    const getRoleIcon = (role)=>{\n        switch(role){\n            case \"admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, this);\n            case \"manager\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, this);\n            case \"member\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 16\n                }, this);\n            case \"viewer\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col w-full h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                className: \"flex items-center justify-between mb-8\",\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: \"visible\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>changePage(\"settings\", {}, true),\n                                className: \"text-secondary hover:text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back to Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-primary flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Companies\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-secondary mt-1\",\n                                        children: \"Manage your companies and access permissions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                        open: isCreateModalOpen,\n                        onOpenChange: setIsCreateModalOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"bg-accent hover:bg-accent/90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Create Company\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                            children: \"Create New Company\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"company-name\",\n                                                        children: \"Company Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"company-name\",\n                                                        value: newCompanyData.name,\n                                                        onChange: (e)=>setNewCompanyData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        placeholder: \"Enter company name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"company-description\",\n                                                        children: \"Description (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                        id: \"company-description\",\n                                                        value: newCompanyData.description,\n                                                        onChange: (e)=>setNewCompanyData((prev)=>({\n                                                                    ...prev,\n                                                                    description: e.target.value\n                                                                })),\n                                                        placeholder: \"Enter company description\",\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"company-logo\",\n                                                        children: \"Company Logo (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"company-logo\",\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) {\n                                                                        const reader = new FileReader();\n                                                                        reader.onload = (event)=>{\n                                                                            var _event_target;\n                                                                            const dataUrl = (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result;\n                                                                            setNewCompanyData((prev)=>({\n                                                                                    ...prev,\n                                                                                    logo_url: dataUrl\n                                                                                }));\n                                                                        };\n                                                                        reader.readAsDataURL(file);\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            newCompanyData.logo_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: newCompanyData.logo_url,\n                                                                        alt: \"Company logo preview\",\n                                                                        className: \"w-8 h-8 object-cover rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setNewCompanyData((prev)=>({\n                                                                                    ...prev,\n                                                                                    logo_url: undefined\n                                                                                })),\n                                                                        children: \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"parent-company\",\n                                                        children: \"Parent Company (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                        value: newCompanyData.parent_company_id || \"none\",\n                                                        onValueChange: (value)=>setNewCompanyData((prev)=>({\n                                                                    ...prev,\n                                                                    parent_company_id: value === \"none\" ? undefined : value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                    placeholder: \"Select parent company\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                        value: \"none\",\n                                                                        children: \"No parent company\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    companies.filter((company)=>company.id !== newCompanyData.id) // Prevent self-reference\n                                                                    .map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: company.id,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-4 h-4 rounded flex items-center justify-center text-white text-xs font-bold\",\n                                                                                        style: {\n                                                                                            backgroundColor: company.primary_color\n                                                                                        },\n                                                                                        children: (0,_types_company__WEBPACK_IMPORTED_MODULE_13__.getCompanyInitials)(company)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                        lineNumber: 262,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    company.name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                lineNumber: 261,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, company.id, false, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"primary-color\",\n                                                                children: \"Primary Color\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"primary-color\",\n                                                                type: \"color\",\n                                                                value: newCompanyData.primary_color,\n                                                                onChange: (e)=>setNewCompanyData((prev)=>({\n                                                                            ...prev,\n                                                                            primary_color: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"secondary-color\",\n                                                                children: \"Secondary Color\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                id: \"secondary-color\",\n                                                                type: \"color\",\n                                                                value: newCompanyData.secondary_color,\n                                                                onChange: (e)=>setNewCompanyData((prev)=>({\n                                                                            ...prev,\n                                                                            secondary_color: e.target.value\n                                                                        }))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        onClick: ()=>setIsCreateModalOpen(false),\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: handleCreateCompany,\n                                                        disabled: !newCompanyData.name.trim(),\n                                                        children: \"Create Company\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            currentCompany && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                variants: itemVariants,\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-accent/20 bg-accent/5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2 text-accent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Current Company\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg\",\n                                        style: {\n                                            backgroundColor: currentCompany.primary_color\n                                        },\n                                        children: currentCompany.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentCompany.logo_url,\n                                            alt: currentCompany.name,\n                                            className: \"w-full h-full object-cover rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 21\n                                        }, this) : (0,_types_company__WEBPACK_IMPORTED_MODULE_13__.getCompanyInitials)(currentCompany.name)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-primary\",\n                                                children: currentCompany.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentCompany.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-secondary text-sm\",\n                                                children: currentCompany.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            currentCompany.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"mt-2 \".concat(_types_company__WEBPACK_IMPORTED_MODULE_13__.COMPANY_ROLE_COLORS[currentCompany.role]),\n                                                children: [\n                                                    getRoleIcon(currentCompany.role),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-1 capitalize\",\n                                                        children: currentCompany.role\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                className: \"flex-1 overflow-y-auto\",\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: \"visible\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.AnimatePresence, {\n                            children: companies.map((company, index)=>{\n                                var _companies_find;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                                    layout: true,\n                                    variants: itemVariants,\n                                    initial: \"hidden\",\n                                    animate: \"visible\",\n                                    exit: \"hidden\",\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"h-full \".concat((currentCompany === null || currentCompany === void 0 ? void 0 : currentCompany.id) === company.id ? \"ring-2 ring-accent\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold\",\n                                                                    style: {\n                                                                        backgroundColor: company.primary_color\n                                                                    },\n                                                                    children: company.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: company.logo_url,\n                                                                        alt: company.name,\n                                                                        className: \"w-full h-full object-cover rounded-lg\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this) : (0,_types_company__WEBPACK_IMPORTED_MODULE_13__.getCompanyInitials)(company.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                                    className: \"text-lg\",\n                                                                                    children: company.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                company.parent_company_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Subsidiary\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 396,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                companies.some((c)=>c.parent_company_id === company.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Parent\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 401,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        company.role && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"mt-1 \".concat(_types_company__WEBPACK_IMPORTED_MODULE_13__.COMPANY_ROLE_COLORS[company.role]),\n                                                                            children: [\n                                                                                getRoleIcon(company.role),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-1 capitalize\",\n                                                                                    children: company.role\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        company.parent_company_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                                            children: [\n                                                                                \"Parent: \",\n                                                                                ((_companies_find = companies.find((c)=>c.id === company.parent_company_id)) === null || _companies_find === void 0 ? void 0 : _companies_find.name) || \"Unknown\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        (0,_types_company__WEBPACK_IMPORTED_MODULE_13__.canManageCompany)(company) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>openEditModal(company),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialog, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                className: \"text-destructive hover:text-destructive\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                lineNumber: 434,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogHeader, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogTitle, {\n                                                                                            children: \"Delete Company\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                            lineNumber: 440,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogDescription, {\n                                                                                            children: [\n                                                                                                'Are you sure you want to delete \"',\n                                                                                                company.name,\n                                                                                                '\"? This action cannot be undone.'\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                            lineNumber: 441,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 439,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogFooter, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogCancel, {\n                                                                                            children: \"Cancel\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                            lineNumber: 446,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogAction, {\n                                                                                            onClick: ()=>handleDeleteCompany(company.id),\n                                                                                            className: \"bg-destructive hover:bg-destructive/90\",\n                                                                                            children: \"Delete\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                            lineNumber: 447,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: [\n                                                    company.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-secondary text-sm mb-4 line-clamp-2\",\n                                                        children: company.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            (currentCompany === null || currentCompany === void 0 ? void 0 : currentCompany.id) !== company.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>switchCompany(company.id),\n                                                                className: \"flex-1\",\n                                                                children: \"Switch to\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            (currentCompany === null || currentCompany === void 0 ? void 0 : currentCompany.id) === company.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"flex-1 justify-center\",\n                                                                children: \"Current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this)\n                                }, company.id, false, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    companies.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"w-16 h-16 text-secondary mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-primary mb-2\",\n                                children: \"No companies found\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-secondary mb-4\",\n                                children: \"Create your first company to get started\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isEditModalOpen,\n                onOpenChange: setIsEditModalOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: \"Edit Company\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this),\n                        editingCompany && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"edit-company-name\",\n                                            children: \"Company Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"edit-company-name\",\n                                            value: editingCompany.name,\n                                            onChange: (e)=>setEditingCompany((prev)=>prev ? {\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    } : null),\n                                            placeholder: \"Enter company name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"edit-company-description\",\n                                            children: \"Description (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                            id: \"edit-company-description\",\n                                            value: editingCompany.description || \"\",\n                                            onChange: (e)=>setEditingCompany((prev)=>prev ? {\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    } : null),\n                                            placeholder: \"Enter company description\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"edit-company-logo\",\n                                            children: \"Company Logo (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"edit-company-logo\",\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: (e)=>{\n                                                        var _e_target_files;\n                                                        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                        if (file) {\n                                                            const reader = new FileReader();\n                                                            reader.onload = (event)=>{\n                                                                var _event_target;\n                                                                const dataUrl = (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result;\n                                                                setEditingCompany((prev)=>prev ? {\n                                                                        ...prev,\n                                                                        logo_url: dataUrl\n                                                                    } : null);\n                                                            };\n                                                            reader.readAsDataURL(file);\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this),\n                                                editingCompany.logo_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: editingCompany.logo_url,\n                                                            alt: \"Company logo preview\",\n                                                            className: \"w-8 h-8 object-cover rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setEditingCompany((prev)=>prev ? {\n                                                                        ...prev,\n                                                                        logo_url: undefined\n                                                                    } : null),\n                                                            children: \"Remove\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"edit-parent-company\",\n                                            children: \"Parent Company (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                            value: editingCompany.parent_company_id || \"\",\n                                            onValueChange: (value)=>setEditingCompany((prev)=>prev ? {\n                                                        ...prev,\n                                                        parent_company_id: value || undefined\n                                                    } : null),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                        placeholder: \"Select parent company\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                            value: \"\",\n                                                            children: \"No parent company\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        companies.filter((company)=>company.id !== editingCompany.id) // Prevent self-reference\n                                                        .map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                value: company.id,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 rounded flex items-center justify-center text-white text-xs font-bold\",\n                                                                            style: {\n                                                                                backgroundColor: company.primary_color\n                                                                            },\n                                                                            children: (0,_types_company__WEBPACK_IMPORTED_MODULE_13__.getCompanyInitials)(company)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        company.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, company.id, false, {\n                                                                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"edit-primary-color\",\n                                                    children: \"Primary Color\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"edit-primary-color\",\n                                                    type: \"color\",\n                                                    value: editingCompany.primary_color,\n                                                    onChange: (e)=>setEditingCompany((prev)=>prev ? {\n                                                                ...prev,\n                                                                primary_color: e.target.value\n                                                            } : null)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"edit-secondary-color\",\n                                                    children: \"Secondary Color\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"edit-secondary-color\",\n                                                    type: \"color\",\n                                                    value: editingCompany.secondary_color,\n                                                    onChange: (e)=>setEditingCompany((prev)=>prev ? {\n                                                                ...prev,\n                                                                secondary_color: e.target.value\n                                                            } : null)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setIsEditModalOpen(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleEditCompany,\n                                            disabled: !editingCompany.name.trim(),\n                                            children: \"Save Changes\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-background/80 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Building2_Crown_Edit_Eye_Loader2_Plus_Shield_Trash2_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                    className: \"w-8 h-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n                lineNumber: 634,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI360\\\\Ai360\\\\AI360_App_V3\\\\frontend\\\\app\\\\pages\\\\CompaniesPage.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(CompaniesPage, \"cTf4Asvzw0vZCAWc+GljrlDaYeo=\", false, function() {\n    return [\n        _components_contexts_CompanyContext__WEBPACK_IMPORTED_MODULE_11__.useCompanies\n    ];\n});\n_c = CompaniesPage;\nvar _c;\n$RefreshReg$(_c, \"CompaniesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pages/CompaniesPage.tsx\n"));

/***/ })

});