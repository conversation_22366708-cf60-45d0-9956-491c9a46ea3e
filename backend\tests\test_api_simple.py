#!/usr/bin/env python3
"""
Simple API test with file output.
"""

import requests
import json

def test_api():
    """Test the API and write results to file."""
    results = []
    
    # Test health endpoint
    try:
        response = requests.get("http://localhost:8000/api/health")
        results.append(f"Health endpoint - Status: {response.status_code}")
        results.append(f"Health endpoint - Response: {response.text}")
    except Exception as e:
        results.append(f"Health endpoint error: {e}")
    
    results.append("")
    
    # Test companies GET
    try:
        response = requests.get("http://localhost:8000/api/companies?user_id=test_user")
        results.append(f"Get companies - Status: {response.status_code}")
        results.append(f"Get companies - Response: {response.text}")
    except Exception as e:
        results.append(f"Get companies error: {e}")
    
    results.append("")
    
    # Test companies POST
    try:
        data = {
            "name": "Test Company API",
            "description": "A test company created via API"
        }
        response = requests.post(
            "http://localhost:8000/api/companies?created_by=test_user",
            json=data
        )
        results.append(f"Create company - Status: {response.status_code}")
        results.append(f"Create company - Response: {response.text}")
    except Exception as e:
        results.append(f"Create company error: {e}")
    
    # Write results to file
    with open("api_test_results.txt", "w") as f:
        f.write("\n".join(results))
    
    print("API test completed. Results written to api_test_results.txt")

if __name__ == "__main__":
    test_api()
