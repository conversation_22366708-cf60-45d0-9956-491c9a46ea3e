#!/usr/bin/env python3
"""
Test script for company support migration.
This script creates sample data and tests the migration process.
"""

import sys
import os
import sqlite3
import json
import uuid
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from ai360.api.migrations.add_company_support import CompanySupportMigration
from ai360.api.models.project import ProjectDatabase
from ai360.api.models.user_preferences import UserPreferencesDatabase


def create_test_data():
    """Create test data in the old schema format."""
    print("📝 Creating test data...")
    
    # Create test projects
    project_db = ProjectDatabase()
    test_projects = [
        {
            'id': str(uuid.uuid4()),
            'name': 'Test Project 1',
            'client': 'Test Client A',
            'status': 'Preparing',
            'privacy': 'Private',
            'summary': 'This is a test project',
            'user_id': 'test_user_1'
        },
        {
            'id': str(uuid.uuid4()),
            'name': 'Test Project 2',
            'client': 'Test Client B',
            'status': 'Writing',
            'privacy': 'Shared',
            'summary': 'Another test project',
            'user_id': 'test_user_2'
        }
    ]
    
    # Note: This will fail if company_id is required, which is expected
    # We'll create the data directly in the database using the old schema
    
    # Create projects using direct SQL to simulate old schema
    backend_dir = Path(__file__).parent
    projects_db_path = backend_dir / "data" / "projects.db"
    
    # Ensure data directory exists
    os.makedirs(projects_db_path.parent, exist_ok=True)
    
    with sqlite3.connect(projects_db_path) as conn:
        # Create old schema table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                client TEXT,
                status TEXT NOT NULL,
                privacy TEXT NOT NULL DEFAULT 'Private',
                start_date TEXT,
                deadline TEXT,
                summary TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                user_id TEXT NOT NULL,
                metadata TEXT DEFAULT '{}'
            )
        """)
        
        # Insert test data
        now = datetime.utcnow().isoformat()
        for project in test_projects:
            conn.execute("""
                INSERT INTO projects (
                    id, name, client, status, privacy, start_date, deadline,
                    summary, created_at, updated_at, user_id, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                project['id'],
                project['name'],
                project.get('client'),
                project['status'],
                project['privacy'],
                project.get('start_date'),
                project.get('deadline'),
                project.get('summary'),
                now,
                now,
                project['user_id'],
                json.dumps({})
            ))
        
        conn.commit()
    
    print(f"   Created {len(test_projects)} test projects")
    
    # Create test user preferences using old schema
    user_prefs_db_path = backend_dir / "data" / "user_preferences.db"
    
    with sqlite3.connect(user_prefs_db_path) as conn:
        # Create old schema tables
        conn.execute("""
            CREATE TABLE IF NOT EXISTS theme_preferences (
                user_id TEXT PRIMARY KEY,
                mode TEXT NOT NULL DEFAULT 'dark',
                color_scheme TEXT NOT NULL DEFAULT 'default',
                auto_switch_enabled BOOLEAN DEFAULT FALSE,
                light_time TEXT DEFAULT '06:00',
                dark_time TEXT DEFAULT '18:00',
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        
        conn.execute("""
            CREATE TABLE IF NOT EXISTS customization_settings (
                user_id TEXT PRIMARY KEY,
                application_name TEXT NOT NULL DEFAULT 'AI360',
                homepage_title TEXT NOT NULL DEFAULT 'AI360',
                homepage_subtitle TEXT NOT NULL DEFAULT 'Intelligent Project Management Platform',
                homepage_description TEXT NOT NULL DEFAULT 'Streamline your project lifecycle with AI-powered insights, collaborative workspaces, and intelligent data management.',
                homepage_features TEXT NOT NULL DEFAULT '[]',
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        
        # Insert test data
        now = datetime.utcnow().isoformat()
        
        conn.execute("""
            INSERT INTO theme_preferences (
                user_id, mode, color_scheme, auto_switch_enabled,
                light_time, dark_time, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, ('test_user_1', 'dark', 'blue', False, '06:00', '18:00', now, now))
        
        conn.execute("""
            INSERT INTO customization_settings (
                user_id, application_name, homepage_title, homepage_subtitle,
                homepage_description, homepage_features, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, ('test_user_1', 'Test AI360', 'Welcome to Test AI360', 'Test Platform',
              'Test description', '[]', now, now))
        
        conn.commit()
    
    print("   Created test user preferences")
    print("✅ Test data created successfully")


def verify_migration():
    """Verify that the migration was successful."""
    print("🔍 Verifying migration...")
    
    backend_dir = Path(__file__).parent
    projects_db_path = backend_dir / "data" / "projects.db"
    user_prefs_db_path = backend_dir / "data" / "user_preferences.db"
    companies_db_path = backend_dir / "data" / "companies.db"
    
    # Check companies database
    if companies_db_path.exists():
        with sqlite3.connect(companies_db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM companies")
            company_count = cursor.fetchone()[0]
            print(f"   ✅ Companies database: {company_count} companies found")
    else:
        print("   ❌ Companies database not found")
        return False
    
    # Check projects table
    if projects_db_path.exists():
        with sqlite3.connect(projects_db_path) as conn:
            # Check schema
            cursor = conn.execute("PRAGMA table_info(projects)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'company_id' in columns:
                print("   ✅ Projects table has company_id column")
                
                # Check data
                cursor = conn.execute("SELECT COUNT(*) FROM projects WHERE company_id IS NOT NULL")
                project_count = cursor.fetchone()[0]
                print(f"   ✅ Projects with company_id: {project_count}")
            else:
                print("   ❌ Projects table missing company_id column")
                return False
    else:
        print("   ❌ Projects database not found")
        return False
    
    # Check user preferences table
    if user_prefs_db_path.exists():
        with sqlite3.connect(user_prefs_db_path) as conn:
            # Check schema
            cursor = conn.execute("PRAGMA table_info(theme_preferences)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'company_id' in columns:
                print("   ✅ Theme preferences table has company_id column")
                
                # Check data
                cursor = conn.execute("SELECT COUNT(*) FROM theme_preferences WHERE company_id IS NOT NULL")
                prefs_count = cursor.fetchone()[0]
                print(f"   ✅ Theme preferences with company_id: {prefs_count}")
            else:
                print("   ❌ Theme preferences table missing company_id column")
                return False
    else:
        print("   ❌ User preferences database not found")
        return False
    
    print("✅ Migration verification successful")
    return True


def main():
    """Main test function."""
    print("🧪 Testing Company Support Migration")
    print("=" * 50)
    
    try:
        # Step 1: Create test data
        create_test_data()
        print()
        
        # Step 2: Run migration
        print("🚀 Running migration...")
        migration = CompanySupportMigration()
        success = migration.run_migration()
        
        if not success:
            print("❌ Migration failed!")
            return False
        
        print("✅ Migration completed successfully")
        print()
        
        # Step 3: Verify migration
        if verify_migration():
            print()
            print("🎉 All tests passed! Migration is working correctly.")
            return True
        else:
            print()
            print("❌ Migration verification failed!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
