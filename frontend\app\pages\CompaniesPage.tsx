"use client";

import React, { useContext, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import {
  Building2,
  Plus,
  Edit,
  Trash2,
  Users,
  Crown,
  Shield,
  Eye,
  ArrowLeft,
  Settings,
  Palette,
  Loader2,
  ChevronDown
} from 'lucide-react';

import { useCompanies } from '../components/contexts/CompanyContext';
import { RouterContext } from '../components/contexts/RouterContext';
import { Company, CreateCompanyData, UpdateCompanyData, getCompanyInitials, COMPANY_ROLE_COLORS, COMPANY_ROLE_DESCRIPTIONS, canManageCompany } from '../types/company';

export default function CompaniesPage() {
  const { 
    companies, 
    currentCompany, 
    setCurrentCompany, 
    createCompany, 
    updateCompany, 
    deleteCompany, 
    switchCompany,
    loading, 
    error 
  } = useCompanies();
  const { changePage } = useContext(RouterContext);

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [newCompanyData, setNewCompanyData] = useState<CreateCompanyData>({
    name: "",
    description: "",
    primary_color: "#3B82F6",
    secondary_color: "#1E40AF"
  });

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const handleCreateCompany = async () => {
    if (newCompanyData.name.trim()) {
      try {
        await createCompany(newCompanyData);
        setNewCompanyData({
          name: "",
          description: "",
          primary_color: "#3B82F6",
          secondary_color: "#1E40AF"
        });
        setIsCreateModalOpen(false);
      } catch (err) {
        console.error("Failed to create company:", err);
      }
    }
  };

  const handleEditCompany = async () => {
    if (editingCompany && editingCompany.name.trim()) {
      try {
        const updateData: UpdateCompanyData = {
          name: editingCompany.name,
          description: editingCompany.description,
          logo_url: editingCompany.logo_url,
          primary_color: editingCompany.primary_color,
          secondary_color: editingCompany.secondary_color,
          parent_company_id: editingCompany.parent_company_id
        };
        await updateCompany(editingCompany.id, updateData);
        setEditingCompany(null);
        setIsEditModalOpen(false);
      } catch (err) {
        console.error("Failed to update company:", err);
      }
    }
  };

  const handleDeleteCompany = async (companyId: string) => {
    try {
      await deleteCompany(companyId);
      // If we deleted the current company, clear it
      if (currentCompany?.id === companyId) {
        setCurrentCompany(null);
      }
    } catch (err) {
      console.error("Failed to delete company:", err);
    }
  };

  const openEditModal = (company: Company) => {
    setEditingCompany({ ...company });
    setIsEditModalOpen(true);
  };

  const getRoleIcon = (role?: string) => {
    switch (role) {
      case "admin":
        return <Crown className="w-4 h-4" />;
      case "manager":
        return <Shield className="w-4 h-4" />;
      case "member":
        return <Users className="w-4 h-4" />;
      case "viewer":
        return <Eye className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  return (
    <div className="flex flex-col w-full h-screen p-6">
      {/* Header */}
      <motion.div 
        className="flex items-center justify-between mb-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => changePage("settings", {}, true)}
            className="text-secondary hover:text-primary"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Settings
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-primary flex items-center gap-3">
              <Building2 className="w-8 h-8" />
              Companies
            </h1>
            <p className="text-secondary mt-1">
              Manage your companies and access permissions
            </p>
          </div>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button className="bg-accent hover:bg-accent/90">
              <Plus className="w-4 h-4 mr-2" />
              Create Company
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Company</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="company-name">Company Name</Label>
                <Input
                  id="company-name"
                  value={newCompanyData.name}
                  onChange={(e) => setNewCompanyData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter company name"
                />
              </div>
              <div>
                <Label htmlFor="company-description">Description (Optional)</Label>
                <Textarea
                  id="company-description"
                  value={newCompanyData.description}
                  onChange={(e) => setNewCompanyData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter company description"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="company-logo">Company Logo (Optional)</Label>
                <div className="space-y-2">
                  <Input
                    id="company-logo"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          const dataUrl = event.target?.result as string;
                          setNewCompanyData(prev => ({ ...prev, logo_url: dataUrl }));
                        };
                        reader.readAsDataURL(file);
                      }
                    }}
                  />
                  {newCompanyData.logo_url && (
                    <div className="flex items-center gap-2">
                      <img
                        src={newCompanyData.logo_url}
                        alt="Company logo preview"
                        className="w-8 h-8 object-cover rounded"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setNewCompanyData(prev => ({ ...prev, logo_url: undefined }))}
                      >
                        Remove
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="parent-company">Parent Company (Optional)</Label>
                <Select
                  value={newCompanyData.parent_company_id || "none"}
                  onValueChange={(value) => setNewCompanyData(prev => ({
                    ...prev,
                    parent_company_id: value === "none" ? undefined : value
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent company" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No parent company</SelectItem>
                    {companies
                      .filter(company => company.id !== newCompanyData.id) // Prevent self-reference
                      .map(company => (
                        <SelectItem key={company.id} value={company.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-4 h-4 rounded flex items-center justify-center text-white text-xs font-bold"
                              style={{ backgroundColor: company.primary_color }}
                            >
                              {getCompanyInitials(company)}
                            </div>
                            {company.name}
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <Input
                    id="primary-color"
                    type="color"
                    value={newCompanyData.primary_color}
                    onChange={(e) => setNewCompanyData(prev => ({ ...prev, primary_color: e.target.value }))}
                  />
                </div>
                <div className="flex-1">
                  <Label htmlFor="secondary-color">Secondary Color</Label>
                  <Input
                    id="secondary-color"
                    type="color"
                    value={newCompanyData.secondary_color}
                    onChange={(e) => setNewCompanyData(prev => ({ ...prev, secondary_color: e.target.value }))}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCompany} disabled={!newCompanyData.name.trim()}>
                  Create Company
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </motion.div>

      {/* Current Company */}
      {currentCompany && (
        <motion.div variants={itemVariants} className="mb-6">
          <Card className="border-accent/20 bg-accent/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-accent">
                <Crown className="w-5 h-5" />
                Current Company
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div 
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg"
                  style={{ backgroundColor: currentCompany.primary_color }}
                >
                  {currentCompany.logo_url ? (
                    <img 
                      src={currentCompany.logo_url} 
                      alt={currentCompany.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    getCompanyInitials(currentCompany.name)
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-primary">{currentCompany.name}</h3>
                  {currentCompany.description && (
                    <p className="text-secondary text-sm">{currentCompany.description}</p>
                  )}
                  {currentCompany.role && (
                    <Badge 
                      variant="outline" 
                      className={`mt-2 ${COMPANY_ROLE_COLORS[currentCompany.role as keyof typeof COMPANY_ROLE_COLORS]}`}
                    >
                      {getRoleIcon(currentCompany.role)}
                      <span className="ml-1 capitalize">{currentCompany.role}</span>
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Companies List */}
      <motion.div 
        className="flex-1 overflow-y-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {companies.map((company, index) => (
              <motion.div
                key={company.id}
                layout
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                transition={{ delay: index * 0.1 }}
              >
                <Card className={`h-full ${currentCompany?.id === company.id ? 'ring-2 ring-accent' : ''}`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold"
                          style={{ backgroundColor: company.primary_color }}
                        >
                          {company.logo_url ? (
                            <img 
                              src={company.logo_url} 
                              alt={company.name}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            getCompanyInitials(company.name)
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <CardTitle className="text-lg">{company.name}</CardTitle>
                            {company.parent_company_id && (
                              <Badge variant="secondary" className="text-xs">
                                Subsidiary
                              </Badge>
                            )}
                            {companies.some(c => c.parent_company_id === company.id) && (
                              <Badge variant="outline" className="text-xs">
                                Parent
                              </Badge>
                            )}
                          </div>
                          {company.role && (
                            <Badge
                              variant="outline"
                              className={`mt-1 ${COMPANY_ROLE_COLORS[company.role as keyof typeof COMPANY_ROLE_COLORS]}`}
                            >
                              {getRoleIcon(company.role)}
                              <span className="ml-1 capitalize">{company.role}</span>
                            </Badge>
                          )}
                          {company.parent_company_id && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Parent: {companies.find(c => c.id === company.parent_company_id)?.name || 'Unknown'}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      {canManageCompany(company) && (
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditModal(company)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Company</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{company.name}"? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction 
                                  onClick={() => handleDeleteCompany(company.id)}
                                  className="bg-destructive hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    {company.description && (
                      <p className="text-secondary text-sm mb-4 line-clamp-2">
                        {company.description}
                      </p>
                    )}
                    
                    <div className="flex gap-2">
                      {currentCompany?.id !== company.id && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => switchCompany(company.id)}
                          className="flex-1"
                        >
                          Switch to
                        </Button>
                      )}
                      {currentCompany?.id === company.id && (
                        <Badge variant="secondary" className="flex-1 justify-center">
                          Current
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {companies.length === 0 && (
          <div className="text-center py-12">
            <Building2 className="w-16 h-16 text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-primary mb-2">No companies found</h3>
            <p className="text-secondary mb-4">
              Create your first company to get started
            </p>
          </div>
        )}
      </motion.div>

      {/* Edit Company Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Company</DialogTitle>
          </DialogHeader>
          {editingCompany && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-company-name">Company Name</Label>
                <Input
                  id="edit-company-name"
                  value={editingCompany.name}
                  onChange={(e) => setEditingCompany(prev => prev ? { ...prev, name: e.target.value } : null)}
                  placeholder="Enter company name"
                />
              </div>
              <div>
                <Label htmlFor="edit-company-description">Description (Optional)</Label>
                <Textarea
                  id="edit-company-description"
                  value={editingCompany.description || ""}
                  onChange={(e) => setEditingCompany(prev => prev ? { ...prev, description: e.target.value } : null)}
                  placeholder="Enter company description"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="edit-company-logo">Company Logo (Optional)</Label>
                <div className="space-y-2">
                  <Input
                    id="edit-company-logo"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          const dataUrl = event.target?.result as string;
                          setEditingCompany(prev => prev ? { ...prev, logo_url: dataUrl } : null);
                        };
                        reader.readAsDataURL(file);
                      }
                    }}
                  />
                  {editingCompany.logo_url && (
                    <div className="flex items-center gap-2">
                      <img
                        src={editingCompany.logo_url}
                        alt="Company logo preview"
                        className="w-8 h-8 object-cover rounded"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingCompany(prev => prev ? { ...prev, logo_url: undefined } : null)}
                      >
                        Remove
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="edit-parent-company">Parent Company (Optional)</Label>
                <Select
                  value={editingCompany.parent_company_id || "none"}
                  onValueChange={(value) => setEditingCompany(prev => prev ? {
                    ...prev,
                    parent_company_id: value === "none" ? undefined : value
                  } : null)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent company" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No parent company</SelectItem>
                    {companies
                      .filter(company => company.id !== editingCompany.id) // Prevent self-reference
                      .map(company => (
                        <SelectItem key={company.id} value={company.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-4 h-4 rounded flex items-center justify-center text-white text-xs font-bold"
                              style={{ backgroundColor: company.primary_color }}
                            >
                              {getCompanyInitials(company)}
                            </div>
                            {company.name}
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="edit-primary-color">Primary Color</Label>
                  <Input
                    id="edit-primary-color"
                    type="color"
                    value={editingCompany.primary_color}
                    onChange={(e) => setEditingCompany(prev => prev ? { ...prev, primary_color: e.target.value } : null)}
                  />
                </div>
                <div className="flex-1">
                  <Label htmlFor="edit-secondary-color">Secondary Color</Label>
                  <Input
                    id="edit-secondary-color"
                    type="color"
                    value={editingCompany.secondary_color}
                    onChange={(e) => setEditingCompany(prev => prev ? { ...prev, secondary_color: e.target.value } : null)}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditCompany} disabled={!editingCompany.name.trim()}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {loading && (
        <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      )}
    </div>
  );
}
