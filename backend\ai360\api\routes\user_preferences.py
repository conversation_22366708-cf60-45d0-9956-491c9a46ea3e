"""
User preferences API routes for theme and customization settings.
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Optional

from ai360.api.api_types import (
    ThemePreferencesData,
    ThemePreferencesResponse,
    CustomizationSettingsData,
    CustomizationSettingsResponse,
    HomepageFeature
)
from ai360.api.models.user_preferences import get_preferences_db, UserPreferencesDatabase
from ai360.api.core.log import logger

router = APIRouter()


def get_db() -> UserPreferencesDatabase:
    """Dependency to get the user preferences database."""
    return get_preferences_db()


# Theme Preferences Endpoints
@router.get("/users/{user_id}/theme", response_model=ThemePreferencesResponse)
async def get_theme_preferences(
    user_id: str,
    company_id: str,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Retrieve user's theme preferences for a specific company."""
    try:
        preferences = db.get_theme_preferences(user_id, company_id)

        if not preferences:
            # Return default theme preferences if none exist
            return ThemePreferencesResponse(
                user_id=user_id,
                company_id=company_id,
                mode="dark",
                color_scheme="default",
                auto_switch_enabled=False,
                light_time="06:00",
                dark_time="18:00",
                created_at="",
                updated_at=""
            )

        return ThemePreferencesResponse(**preferences)

    except Exception as e:
        logger.error(f"Error getting theme preferences for user {user_id} in company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get theme preferences")


@router.put("/users/{user_id}/theme", response_model=ThemePreferencesResponse)
async def save_theme_preferences(
    user_id: str,
    preferences: ThemePreferencesData,
    company_id: str,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Save or update user's theme preferences for a specific company."""
    try:
        # Convert Pydantic model to dict
        preferences_dict = preferences.model_dump()

        saved_preferences = db.save_theme_preferences(user_id, company_id, preferences_dict)

        if not saved_preferences:
            raise HTTPException(status_code=500, detail="Failed to save theme preferences")

        return ThemePreferencesResponse(**saved_preferences)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving theme preferences for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save theme preferences")


@router.delete("/users/{user_id}/theme")
async def reset_theme_preferences(
    user_id: str,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Reset user's theme preferences to defaults."""
    try:
        deleted = db.delete_theme_preferences(user_id)
        
        return JSONResponse(
            content={
                "message": "Theme preferences reset to defaults" if deleted else "No theme preferences found",
                "reset": deleted
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error resetting theme preferences for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to reset theme preferences")


# Customization Settings Endpoints
@router.get("/users/{user_id}/customization", response_model=CustomizationSettingsResponse)
async def get_customization_settings(
    user_id: str,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Retrieve user's customization settings."""
    try:
        settings = db.get_customization_settings(user_id)
        
        if not settings:
            # Return default customization settings if none exist
            default_features = [
                {
                    "id": "project-lifecycle",
                    "icon": "🚀",
                    "title": "Project Lifecycle",
                    "description": "Manage projects from preparation to completion with intelligent tracking"
                },
                {
                    "id": "ai-chat",
                    "icon": "🤖",
                    "title": "AI-Powered Chat",
                    "description": "Intelligent conversations with context-aware AI assistance"
                },
                {
                    "id": "data-management",
                    "icon": "📊",
                    "title": "Data Management",
                    "description": "Organize and analyze your project data with powerful tools"
                }
            ]
            
            return CustomizationSettingsResponse(
                user_id=user_id,
                application_name="AI360",
                homepage_title="AI360",
                homepage_subtitle="Intelligent Project Management Platform",
                homepage_description="Streamline your project lifecycle with AI-powered insights, collaborative workspaces, and intelligent data management.",
                homepage_features=[HomepageFeature(**feature) for feature in default_features],
                created_at="",
                updated_at=""
            )
        
        # Convert features to HomepageFeature objects
        settings['homepage_features'] = [
            HomepageFeature(**feature) for feature in settings['homepage_features']
        ]
        
        return CustomizationSettingsResponse(**settings)
        
    except Exception as e:
        logger.error(f"Error getting customization settings for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get customization settings")


@router.put("/users/{user_id}/customization", response_model=CustomizationSettingsResponse)
async def save_customization_settings(
    user_id: str,
    settings: CustomizationSettingsData,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Save or update user's customization settings."""
    try:
        # Convert Pydantic model to dict, excluding None values
        settings_dict = {k: v for k, v in settings.model_dump().items() if v is not None}
        
        saved_settings = db.save_customization_settings(user_id, settings_dict)
        
        if not saved_settings:
            raise HTTPException(status_code=500, detail="Failed to save customization settings")
        
        # Convert features to HomepageFeature objects
        saved_settings['homepage_features'] = [
            HomepageFeature(**feature) for feature in saved_settings['homepage_features']
        ]
        
        return CustomizationSettingsResponse(**saved_settings)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving customization settings for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to save customization settings")


@router.delete("/users/{user_id}/customization")
async def reset_customization_settings(
    user_id: str,
    db: UserPreferencesDatabase = Depends(get_db)
):
    """Reset user's customization settings to defaults."""
    try:
        deleted = db.delete_customization_settings(user_id)
        
        return JSONResponse(
            content={
                "message": "Customization settings reset to defaults" if deleted else "No customization settings found",
                "reset": deleted
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error resetting customization settings for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to reset customization settings")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint for user preferences."""
    try:
        db = get_preferences_db()
        # Simple test to ensure database is accessible
        db.get_theme_preferences("health_check_user")
        return JSONResponse(
            content={"status": "healthy", "service": "user_preferences"},
            status_code=200
        )
    except Exception as e:
        logger.error(f"User preferences health check failed: {str(e)}")
        return JSONResponse(
            content={"status": "unhealthy", "service": "user_preferences", "error": str(e)},
            status_code=503
        )
