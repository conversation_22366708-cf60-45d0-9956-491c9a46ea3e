#!/usr/bin/env python3
"""
Simple test to verify companies router works.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def main():
    try:
        # Test 1: Import companies router
        print("Test 1: Importing companies router...")
        from ai360.api.routes.companies import router
        print(f"SUCCESS: Router imported with {len(router.routes)} routes")
        
        # Test 2: Create test app with companies router
        print("\nTest 2: Creating test app...")
        from fastapi import FastAPI
        test_app = FastAPI()
        test_app.include_router(router, prefix="/api", tags=["companies"])
        
        companies_routes = [
            f"{list(route.methods)} {route.path}" 
            for route in test_app.routes 
            if hasattr(route, 'path') and hasattr(route, 'methods') and '/companies' in route.path
        ]
        print(f"SUCCESS: Test app has {len(companies_routes)} companies routes")
        
        # Test 3: Test with FastAPI TestClient
        print("\nTest 3: Testing with TestClient...")
        from fastapi.testclient import TestClient
        client = TestClient(test_app)
        
        # Test GET companies
        response = client.get("/api/companies?user_id=test_user")
        print(f"GET /api/companies: {response.status_code}")
        
        # Test POST companies
        response = client.post(
            "/api/companies?created_by=test_user",
            json={"name": "Test Company", "description": "Test"}
        )
        print(f"POST /api/companies: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("SUCCESS: Companies router is working correctly!")
            return True
        else:
            print(f"ERROR: POST failed with {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Simple Companies Router Test")
    print("=" * 40)
    
    success = main()
    
    print("\n" + "=" * 40)
    if success:
        print("RESULT: Companies router works correctly!")
        print("ISSUE: Server needs restart to pick up changes")
        print("SOLUTION: Restart FastAPI server")
    else:
        print("RESULT: Companies router has issues")
        print("SOLUTION: Fix router implementation")
