#!/usr/bin/env python3
"""
Simple Multi-Company Test for AI360 Platform
"""

import sys
import os
import tempfile
import sqlite3
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from ai360.api.models.company import CompanyDatabase
    from ai360.api.models.project import ProjectDatabase
    print("✅ Successfully imported database models")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_basic_functionality():
    """Test basic multi-company functionality."""
    print("\n🧪 Testing Basic Multi-Company Functionality")
    print("=" * 50)
    
    # Create temporary database
    temp_dir = tempfile.mkdtemp()
    company_db_path = os.path.join(temp_dir, "test_companies.db")
    project_db_path = os.path.join(temp_dir, "test_projects.db")
    
    try:
        # Initialize databases
        company_db = CompanyDatabase(company_db_path)
        project_db = ProjectDatabase(project_db_path)
        print("✅ Databases initialized successfully")
        
        # Test 1: Create a company
        company_data = {
            "name": "Test Company",
            "description": "A test company",
            "primary_color": "#3B82F6",
            "secondary_color": "#1E40AF"
        }
        
        company = company_db.create_company(company_data, "test_user")
        print(f"✅ Created company: {company['name']} (ID: {company['id']})")
        
        # Test 2: Grant user access
        success = company_db.grant_user_access("test_user", company['id'], "admin", "system")
        print(f"✅ Granted user access: {success}")
        
        # Test 3: Get user companies
        user_companies = company_db.get_companies_for_user("test_user")
        print(f"✅ User has access to {len(user_companies)} companies")
        
        # Test 4: Create a project
        project_data = {
            "id": "test-project-1",
            "name": "Test Project",
            "description": "A test project",
            "user_id": "test_user",
            "company_id": company['id'],
            "status": "Active",
            "privacy": "Private",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "metadata": {}
        }
        
        project = project_db.create_project(project_data)
        print(f"✅ Created project: {project['name']} (ID: {project['id']})")
        
        # Test 5: Get company-scoped projects
        projects = project_db.get_projects_by_user_and_company("test_user", company['id'])
        print(f"✅ Found {len(projects)} projects for company")
        
        # Test 6: Test data isolation
        other_projects = project_db.get_projects_by_user_and_company("test_user", "non-existent-company")
        print(f"✅ Data isolation test: {len(other_projects)} projects for non-existent company")
        
        print("\n🎉 All basic tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("🧹 Cleaned up test environment")
    
    return True

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n✅ Multi-Company Test Suite: PASSED")
        sys.exit(0)
    else:
        print("\n❌ Multi-Company Test Suite: FAILED")
        sys.exit(1)
