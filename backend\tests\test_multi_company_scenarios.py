#!/usr/bin/env python3
"""
Multi-Company Test Scenarios for AI360 Platform

This script tests comprehensive multi-company functionality including:
- Company creation and management
- User access control and permissions
- Data isolation between companies
- Company switching workflows
- Security boundary validation
- Performance with multiple companies
"""

import asyncio
import json
import sqlite3
import tempfile
import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import uuid
from datetime import datetime

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from ai360.api.models.company import CompanyDatabase
from ai360.api.models.project import ProjectDatabase
from ai360.api.models.user_preferences import UserPreferencesDatabase

class MultiCompanyTestRunner:
    """Test runner for multi-company scenarios."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.company_db_path = os.path.join(self.temp_dir, "test_companies.db")
        self.project_db_path = os.path.join(self.temp_dir, "test_projects.db")
        self.preferences_db_path = os.path.join(self.temp_dir, "test_preferences.db")
        
        # Initialize databases
        self.company_db = CompanyDatabase(self.company_db_path)
        self.project_db = ProjectDatabase(self.project_db_path)
        self.preferences_db = UserPreferencesDatabase(self.preferences_db_path)
        
        # Test data
        self.test_users = ["user1", "user2", "user3"]
        self.test_companies = []
        self.test_projects = []
        
        print(f"🧪 Test environment initialized in: {self.temp_dir}")
    
    def cleanup(self):
        """Clean up test environment."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        print("🧹 Test environment cleaned up")
    
    async def run_all_tests(self):
        """Run all multi-company test scenarios."""
        print("\n🚀 Starting Multi-Company Test Scenarios")
        print("=" * 60)
        
        try:
            # Test 1: Company Creation and Management
            await self.test_company_creation_and_management()
            
            # Test 2: User Access Control
            await self.test_user_access_control()
            
            # Test 3: Data Isolation
            await self.test_data_isolation()
            
            # Test 4: Company Switching
            await self.test_company_switching()
            
            # Test 5: Hierarchy and Permissions
            await self.test_company_hierarchy()
            
            # Test 6: Security Boundaries
            await self.test_security_boundaries()
            
            # Test 7: Performance Testing
            await self.test_performance_with_multiple_companies()
            
            print("\n✅ All Multi-Company Tests Completed Successfully!")
            
        except Exception as e:
            print(f"\n❌ Test Failed: {str(e)}")
            raise
        finally:
            self.cleanup()
    
    async def test_company_creation_and_management(self):
        """Test 1: Company Creation and Management."""
        print("\n📋 Test 1: Company Creation and Management")
        print("-" * 40)
        
        # Create test companies
        companies_data = [
            {
                "name": "Acme Corporation",
                "description": "Leading technology company",
                "logo_url": "https://example.com/acme-logo.png",
                "primary_color": "#FF6B35",
                "secondary_color": "#F7931E"
            },
            {
                "name": "Beta Industries",
                "description": "Manufacturing and logistics",
                "logo_url": "https://example.com/beta-logo.png",
                "primary_color": "#2E86AB",
                "secondary_color": "#A23B72"
            },
            {
                "name": "Gamma Solutions",
                "description": "Consulting and services",
                "logo_url": "https://example.com/gamma-logo.png",
                "primary_color": "#F18F01",
                "secondary_color": "#C73E1D"
            }
        ]
        
        for company_data in companies_data:
            company = self.company_db.create_company(company_data, "user1")
            self.test_companies.append(company)
            print(f"✓ Created company: {company['name']} (ID: {company['id']})")
        
        # Test company retrieval
        all_companies = self.company_db.get_all_companies()
        assert len(all_companies) == 3, f"Expected 3 companies, got {len(all_companies)}"
        print(f"✓ Retrieved {len(all_companies)} companies")
        
        # Test company update
        update_data = {"description": "Updated description for Acme Corporation"}
        updated_company = self.company_db.update_company(self.test_companies[0]['id'], update_data, "user1")
        assert updated_company['description'] == update_data['description']
        print("✓ Company update successful")
        
        print("✅ Company Creation and Management: PASSED")
    
    async def test_user_access_control(self):
        """Test 2: User Access Control."""
        print("\n🔐 Test 2: User Access Control")
        print("-" * 40)
        
        # Grant different access levels to users
        access_grants = [
            ("user1", self.test_companies[0]['id'], "admin"),
            ("user1", self.test_companies[1]['id'], "member"),
            ("user2", self.test_companies[0]['id'], "manager"),
            ("user2", self.test_companies[2]['id'], "viewer"),
            ("user3", self.test_companies[1]['id'], "member")
        ]
        
        for user_id, company_id, role in access_grants:
            success = self.company_db.grant_user_access(user_id, company_id, role, "system")
            assert success, f"Failed to grant {role} access to {user_id} for company {company_id}"
            print(f"✓ Granted {role} access to {user_id} for company {company_id}")
        
        # Test user company retrieval
        user1_companies = self.company_db.get_companies_for_user("user1")
        assert len(user1_companies) == 2, f"User1 should have access to 2 companies, got {len(user1_companies)}"
        
        user2_companies = self.company_db.get_companies_for_user("user2")
        assert len(user2_companies) == 2, f"User2 should have access to 2 companies, got {len(user2_companies)}"
        
        user3_companies = self.company_db.get_companies_for_user("user3")
        assert len(user3_companies) == 1, f"User3 should have access to 1 company, got {len(user3_companies)}"
        
        print("✓ User access control working correctly")
        
        # Test role-based permissions
        user1_role = self.company_db.get_user_role("user1", self.test_companies[0]['id'])
        assert user1_role == "admin", f"Expected admin role, got {user1_role}"
        
        user2_role = self.company_db.get_user_role("user2", self.test_companies[0]['id'])
        assert user2_role == "manager", f"Expected manager role, got {user2_role}"
        
        print("✓ Role-based permissions working correctly")
        print("✅ User Access Control: PASSED")
    
    async def test_data_isolation(self):
        """Test 3: Data Isolation."""
        print("\n🔒 Test 3: Data Isolation")
        print("-" * 40)
        
        # Create projects for different companies
        projects_data = [
            {
                "name": "Acme Project Alpha",
                "description": "Secret project for Acme",
                "user_id": "user1",
                "company_id": self.test_companies[0]['id'],
                "status": "Active",
                "privacy": "Private"
            },
            {
                "name": "Beta Project Bravo",
                "description": "Manufacturing project for Beta",
                "user_id": "user3",
                "company_id": self.test_companies[1]['id'],
                "status": "Active",
                "privacy": "Private"
            },
            {
                "name": "Gamma Project Charlie",
                "description": "Consulting project for Gamma",
                "user_id": "user2",
                "company_id": self.test_companies[2]['id'],
                "status": "Active",
                "privacy": "Private"
            }
        ]
        
        for project_data in projects_data:
            project_data['id'] = str(uuid.uuid4())
            project = self.project_db.create_project(project_data)
            self.test_projects.append(project)
            print(f"✓ Created project: {project['name']} for company {project['company_id']}")
        
        # Test company-scoped project retrieval
        acme_projects = self.project_db.get_projects_by_user_and_company("user1", self.test_companies[0]['id'])
        assert len(acme_projects) == 1, f"Expected 1 project for Acme, got {len(acme_projects)}"
        assert acme_projects[0]['name'] == "Acme Project Alpha"
        
        beta_projects = self.project_db.get_projects_by_user_and_company("user3", self.test_companies[1]['id'])
        assert len(beta_projects) == 1, f"Expected 1 project for Beta, got {len(beta_projects)}"
        assert beta_projects[0]['name'] == "Beta Project Bravo"
        
        # Test cross-company access prevention
        user1_beta_projects = self.project_db.get_projects_by_user_and_company("user1", self.test_companies[1]['id'])
        assert len(user1_beta_projects) == 0, "User1 should not see Beta company projects"
        
        print("✓ Data isolation working correctly")
        print("✅ Data Isolation: PASSED")

    async def test_company_switching(self):
        """Test 4: Company Switching."""
        print("\n🔄 Test 4: Company Switching")
        print("-" * 40)

        # Simulate user switching between companies
        user1_companies = self.company_db.get_companies_for_user("user1")

        # Test switching to first company
        current_company = user1_companies[0]
        print(f"✓ User1 switched to: {current_company['name']}")

        # Verify projects are scoped to current company
        projects = self.project_db.get_projects_by_user_and_company("user1", current_company['id'])
        print(f"✓ Found {len(projects)} projects for current company")

        # Test switching to second company
        if len(user1_companies) > 1:
            current_company = user1_companies[1]
            print(f"✓ User1 switched to: {current_company['name']}")

            # Verify different project set
            projects = self.project_db.get_projects_by_user_and_company("user1", current_company['id'])
            print(f"✓ Found {len(projects)} projects for new company")

        print("✅ Company Switching: PASSED")

    async def test_company_hierarchy(self):
        """Test 5: Company Hierarchy."""
        print("\n🏢 Test 5: Company Hierarchy")
        print("-" * 40)

        # Create subsidiary company
        subsidiary_data = {
            "name": "Acme Subsidiary",
            "description": "Subsidiary of Acme Corporation",
            "parent_company_id": self.test_companies[0]['id'],
            "logo_url": "https://example.com/acme-sub-logo.png",
            "primary_color": "#FF6B35",
            "secondary_color": "#F7931E"
        }

        subsidiary = self.company_db.create_company(subsidiary_data, "user1")
        print(f"✓ Created subsidiary: {subsidiary['name']} under {self.test_companies[0]['name']}")

        # Test hierarchy retrieval
        subsidiaries = self.company_db.get_subsidiaries(self.test_companies[0]['id'])
        assert len(subsidiaries) == 1, f"Expected 1 subsidiary, got {len(subsidiaries)}"
        assert subsidiaries[0]['name'] == "Acme Subsidiary"

        # Test parent company retrieval
        parent = self.company_db.get_company_by_id(subsidiary['parent_company_id'])
        assert parent['name'] == "Acme Corporation"

        print("✓ Company hierarchy working correctly")
        print("✅ Company Hierarchy: PASSED")

    async def test_security_boundaries(self):
        """Test 6: Security Boundaries."""
        print("\n🛡️ Test 6: Security Boundaries")
        print("-" * 40)

        # Test unauthorized company access
        unauthorized_user = "unauthorized_user"

        # Try to access companies without permission
        user_companies = self.company_db.get_companies_for_user(unauthorized_user)
        assert len(user_companies) == 0, "Unauthorized user should not have access to any companies"
        print("✓ Unauthorized user correctly blocked from company access")

        # Try to access projects from unauthorized company
        unauthorized_projects = self.project_db.get_projects_by_user_and_company(
            unauthorized_user, self.test_companies[0]['id']
        )
        assert len(unauthorized_projects) == 0, "Unauthorized user should not see any projects"
        print("✓ Unauthorized user correctly blocked from project access")

        # Test role-based restrictions
        viewer_user = "user2"  # Has viewer role for company 2
        viewer_company_id = self.test_companies[2]['id']

        # Viewer should be able to read but not modify
        role = self.company_db.get_user_role(viewer_user, viewer_company_id)
        assert role == "viewer", f"Expected viewer role, got {role}"
        print("✓ Role-based access control working correctly")

        print("✅ Security Boundaries: PASSED")

    async def test_performance_with_multiple_companies(self):
        """Test 7: Performance with Multiple Companies."""
        print("\n⚡ Test 7: Performance with Multiple Companies")
        print("-" * 40)

        import time

        # Create many companies for performance testing
        start_time = time.time()

        performance_companies = []
        for i in range(50):
            company_data = {
                "name": f"Performance Test Company {i}",
                "description": f"Test company {i} for performance testing",
                "primary_color": "#3B82F6",
                "secondary_color": "#1E40AF"
            }
            company = self.company_db.create_company(company_data, "performance_user")
            performance_companies.append(company)

        creation_time = time.time() - start_time
        print(f"✓ Created 50 companies in {creation_time:.2f} seconds")

        # Test bulk retrieval performance
        start_time = time.time()
        all_companies = self.company_db.get_all_companies()
        retrieval_time = time.time() - start_time

        assert len(all_companies) >= 50, f"Expected at least 50 companies, got {len(all_companies)}"
        print(f"✓ Retrieved {len(all_companies)} companies in {retrieval_time:.2f} seconds")

        # Test user company access performance
        start_time = time.time()

        # Grant access to many companies for performance user
        for company in performance_companies[:20]:  # Grant access to 20 companies
            self.company_db.grant_user_access("performance_user", company['id'], "member", "system")

        user_companies = self.company_db.get_companies_for_user("performance_user")
        access_time = time.time() - start_time

        assert len(user_companies) == 20, f"Expected 20 companies for user, got {len(user_companies)}"
        print(f"✓ User access to 20 companies processed in {access_time:.2f} seconds")

        print("✅ Performance Testing: PASSED")


async def main():
    """Main test runner."""
    test_runner = MultiCompanyTestRunner()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
