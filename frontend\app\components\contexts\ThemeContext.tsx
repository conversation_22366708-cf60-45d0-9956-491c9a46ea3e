"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { ThemeMode, ColorScheme, ThemeSettings, DEFAULT_THEME_SETTINGS, THEME_STORAGE_KEY } from '../../types/theme';
import { applyTheme, getStoredTheme, saveTheme } from '../../utils/themeUtils';
import { useCompanies } from './CompanyContext';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const USER_ID = "default_user"; // TODO: Replace with actual user authentication

// API helper functions
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
    throw new Error(errorData.detail || `HTTP ${response.status}`);
  }

  return response.json();
};

interface ThemeContextType {
  mode: ThemeMode;
  colorScheme: ColorScheme;
  settings: ThemeSettings;
  setMode: (mode: ThemeMode) => void;
  setColorScheme: (scheme: ColorScheme) => void;
  updateSettings: (settings: Partial<ThemeSettings>) => void;
  toggleMode: () => void;
  resetToDefaults: () => void;
  isLoading: boolean;
  error: string | null;
  setError: (error: string | null) => void;
  saveToBackend: () => Promise<void>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { currentCompany } = useCompanies();
  const [settings, setSettings] = useState<ThemeSettings>(DEFAULT_THEME_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load theme preferences from backend API
  const loadThemeFromBackend = useCallback(async () => {
    if (!currentCompany) {
      // If no company is selected, use localStorage fallback
      try {
        const stored = getStoredTheme();
        const fallbackSettings: ThemeSettings = {
          mode: stored.mode,
          colorScheme: stored.colorScheme,
        };

        const fullSettings = localStorage.getItem(THEME_STORAGE_KEY);
        if (fullSettings) {
          const parsed = JSON.parse(fullSettings);
          Object.assign(fallbackSettings, parsed);
        }

        setSettings(fallbackSettings);
        applyTheme(fallbackSettings.mode, fallbackSettings.colorScheme);
      } catch (localError) {
        console.error('Error loading from localStorage:', localError);
        setSettings(DEFAULT_THEME_SETTINGS);
        applyTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
      }
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany.id}`);

      const backendSettings: ThemeSettings = {
        mode: response.mode as ThemeMode,
        colorScheme: response.color_scheme as ColorScheme,
        autoSwitchTime: response.auto_switch_enabled ? {
          lightTime: response.light_time || '06:00',
          darkTime: response.dark_time || '18:00'
        } : undefined
      };

      setSettings(backendSettings);
      applyTheme(backendSettings.mode, backendSettings.colorScheme);

      // Save to localStorage as backup
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(backendSettings));
      saveTheme(backendSettings.mode, backendSettings.colorScheme);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load theme preferences';
      setError(errorMessage);
      console.error("Failed to load theme from backend:", err);

      // Fallback to localStorage
      try {
        const stored = getStoredTheme();
        const fallbackSettings: ThemeSettings = {
          mode: stored.mode,
          colorScheme: stored.colorScheme,
        };

        const fullSettings = localStorage.getItem(THEME_STORAGE_KEY);
        if (fullSettings) {
          const parsed = JSON.parse(fullSettings);
          Object.assign(fallbackSettings, parsed);
        }

        setSettings(fallbackSettings);
        applyTheme(fallbackSettings.mode, fallbackSettings.colorScheme);
      } catch (localError) {
        console.error('Error loading from localStorage:', localError);
        setSettings(DEFAULT_THEME_SETTINGS);
        applyTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentCompany]);

  // Save theme preferences to backend API
  const saveToBackend = useCallback(async () => {
    if (!currentCompany) {
      // If no company is selected, only save to localStorage
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(settings));
      saveTheme(settings.mode, settings.colorScheme);
      return;
    }

    try {
      setError(null);

      const themeData = {
        mode: settings.mode,
        colorScheme: settings.colorScheme,
        autoSwitchEnabled: !!settings.autoSwitchTime,
        lightTime: settings.autoSwitchTime?.lightTime || '06:00',
        darkTime: settings.autoSwitchTime?.darkTime || '18:00'
      };

      await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany.id}`, {
        method: 'PUT',
        body: JSON.stringify(themeData),
      });

      // Also save to localStorage as backup
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(settings));
      saveTheme(settings.mode, settings.colorScheme);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save theme preferences';
      setError(errorMessage);
      console.error("Failed to save theme to backend:", err);

      // Fallback to localStorage only
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(settings));
      saveTheme(settings.mode, settings.colorScheme);
    }
  }, [settings, currentCompany]);

  // Initialize theme on mount
  useEffect(() => {
    loadThemeFromBackend();
  }, [loadThemeFromBackend]);

  const setMode = useCallback(async (mode: ThemeMode) => {
    const newSettings = { ...settings, mode };
    setSettings(newSettings);
    applyTheme(mode, settings.colorScheme);

    // Save to backend (with localStorage fallback)
    try {
      setError(null);
      const themeData = {
        mode,
        colorScheme: settings.colorScheme,
        autoSwitchEnabled: !!newSettings.autoSwitchTime,
        lightTime: newSettings.autoSwitchTime?.lightTime || '06:00',
        darkTime: newSettings.autoSwitchTime?.darkTime || '18:00'
      };

      await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany?.id}`, {
        method: 'PUT',
        body: JSON.stringify(themeData),
      });

      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(mode, settings.colorScheme);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save theme mode';
      setError(errorMessage);
      console.error("Failed to save theme mode:", err);

      // Fallback to localStorage
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(mode, settings.colorScheme);
    }
  }, [settings, currentCompany]);

  const setColorScheme = useCallback(async (colorScheme: ColorScheme) => {
    const newSettings = { ...settings, colorScheme };
    setSettings(newSettings);
    applyTheme(settings.mode, colorScheme);

    // Save to backend (with localStorage fallback)
    try {
      setError(null);
      const themeData = {
        mode: settings.mode,
        colorScheme,
        autoSwitchEnabled: !!newSettings.autoSwitchTime,
        lightTime: newSettings.autoSwitchTime?.lightTime || '06:00',
        darkTime: newSettings.autoSwitchTime?.darkTime || '18:00'
      };

      await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany?.id}`, {
        method: 'PUT',
        body: JSON.stringify(themeData),
      });

      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(settings.mode, colorScheme);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save color scheme';
      setError(errorMessage);
      console.error("Failed to save color scheme:", err);

      // Fallback to localStorage
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(settings.mode, colorScheme);
    }
  }, [settings]);

  const updateSettings = useCallback(async (updates: Partial<ThemeSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    applyTheme(newSettings.mode, newSettings.colorScheme);

    // Save to backend (with localStorage fallback)
    try {
      setError(null);
      const themeData = {
        mode: newSettings.mode,
        colorScheme: newSettings.colorScheme,
        autoSwitchEnabled: !!newSettings.autoSwitchTime,
        lightTime: newSettings.autoSwitchTime?.lightTime || '06:00',
        darkTime: newSettings.autoSwitchTime?.darkTime || '18:00'
      };

      await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany?.id}`, {
        method: 'PUT',
        body: JSON.stringify(themeData),
      });

      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(newSettings.mode, newSettings.colorScheme);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save theme settings';
      setError(errorMessage);
      console.error("Failed to save theme settings:", err);

      // Fallback to localStorage
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(newSettings));
      saveTheme(newSettings.mode, newSettings.colorScheme);
    }
  }, [settings, currentCompany]);

  const toggleMode = useCallback(() => {
    const newMode = settings.mode === 'dark' ? 'light' : 'dark';
    setMode(newMode);
  }, [settings.mode, setMode]);

  const resetToDefaults = useCallback(async () => {
    try {
      setError(null);
      // Reset on backend
      await apiCall(`/api/users/${USER_ID}/theme?company_id=${currentCompany?.id}`, {
        method: 'DELETE',
      });

      // Reset locally
      setSettings(DEFAULT_THEME_SETTINGS);
      applyTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(DEFAULT_THEME_SETTINGS));
      saveTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset theme preferences';
      setError(errorMessage);
      console.error("Failed to reset theme preferences:", err);

      // Fallback to local reset
      setSettings(DEFAULT_THEME_SETTINGS);
      applyTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(DEFAULT_THEME_SETTINGS));
      saveTheme(DEFAULT_THEME_SETTINGS.mode, DEFAULT_THEME_SETTINGS.colorScheme);
    }
  }, []);

  const value: ThemeContextType = {
    mode: settings.mode,
    colorScheme: settings.colorScheme,
    settings,
    setMode,
    setColorScheme,
    updateSettings,
    toggleMode,
    resetToDefaults,
    isLoading,
    error,
    setError,
    saveToBackend,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
