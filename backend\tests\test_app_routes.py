#!/usr/bin/env python3
"""
Test the FastAPI app routes directly.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_app_routes():
    """Test that the app routes are registered correctly."""
    global results
    try:
        from ai360.api.app import app
        results.append("✅ App imported successfully")

        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append(f"{list(route.methods)} {route.path}")
            elif hasattr(route, 'path'):
                routes.append(f"[MOUNT] {route.path}")

        results.append(f"\n📋 Found {len(routes)} routes:")
        for route in sorted(routes):
            results.append(f"  {route}")

        # Check for companies routes specifically
        companies_routes = [route for route in routes if '/companies' in route]
        results.append(f"\n🏢 Companies routes ({len(companies_routes)}):")
        for route in companies_routes:
            results.append(f"  {route}")

        if companies_routes:
            results.append("✅ Companies routes are registered!")
        else:
            results.append("❌ No companies routes found!")

        return len(companies_routes) > 0

    except Exception as e:
        results.append(f"❌ App import failed: {e}")
        import traceback
        results.append(traceback.format_exc())
        return False

def test_companies_router():
    """Test the companies router directly."""
    global results
    try:
        from ai360.api.routes.companies import router
        results.append("\n🔍 Testing companies router directly...")

        routes = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append(f"{list(route.methods)} {route.path}")

        results.append(f"📋 Companies router has {len(routes)} routes:")
        for route in routes:
            results.append(f"  {route}")

        return len(routes) > 0

    except Exception as e:
        results.append(f"❌ Companies router test failed: {e}")
        import traceback
        results.append(traceback.format_exc())
        return False

if __name__ == "__main__":
    results = []
    results.append("Testing FastAPI App Routes")
    results.append("=" * 50)

    app_ok = test_app_routes()
    router_ok = test_companies_router()

    results.append("\n" + "=" * 50)
    results.append(f"App routes test: {'✅' if app_ok else '❌'}")
    results.append(f"Companies router test: {'✅' if router_ok else '❌'}")

    if app_ok and router_ok:
        results.append("\n🎉 All route tests passed!")
    else:
        results.append("\n⚠️ Some route tests failed.")

    # Write to file
    with open("route_test_results.txt", "w") as f:
        f.write("\n".join(results))

    print("Route test completed. Results written to route_test_results.txt")
