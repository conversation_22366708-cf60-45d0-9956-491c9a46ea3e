#!/usr/bin/env python3
"""
Minimal test to create a working companies API.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import uvicorn

# Simple data models
class CreateCompany(BaseModel):
    name: str
    description: Optional[str] = None

class Company(BaseModel):
    id: str
    name: str
    description: Optional[str] = None

# Create FastAPI app
app = FastAPI(title="Minimal Companies Test")

# In-memory storage for testing
companies_db = {}

@app.get("/api/health")
async def health():
    return {"status": "healthy", "service": "minimal_companies_test"}

@app.get("/api/companies")
async def get_companies(user_id: str):
    """Get companies for a user."""
    return {"companies": list(companies_db.values()), "total": len(companies_db)}

@app.post("/api/companies")
async def create_company(company_data: CreateCompany, created_by: str):
    """Create a new company."""
    import uuid
    company_id = str(uuid.uuid4())
    
    company = Company(
        id=company_id,
        name=company_data.name,
        description=company_data.description
    )
    
    companies_db[company_id] = company.dict()
    return company

if __name__ == "__main__":
    print("Starting minimal companies test server...")
    print("Test endpoints:")
    print("  GET  http://localhost:8001/api/health")
    print("  GET  http://localhost:8001/api/companies?user_id=test")
    print("  POST http://localhost:8001/api/companies?created_by=test")
    print("\nPress Ctrl+C to stop")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, reload=True)
