"""
Project management API routes.
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import Optional
import uuid

from ai360.api.api_types import (
    CreateProjectData,
    UpdateProjectData,
    ProjectResponse,
    ProjectListResponse,
    ProjectStatsResponse
)
from ai360.api.models.project import get_project_db, ProjectDatabase
from ai360.api.core.log import logger

router = APIRouter()


def get_db() -> ProjectDatabase:
    """Dependency to get the project database."""
    return get_project_db()


@router.post("/{user_id}/projects", response_model=ProjectResponse)
async def create_project(
    user_id: str,
    project_data: CreateProjectData,
    company_id: str,
    db: ProjectDatabase = Depends(get_db)
):
    """Create a new project for a user in a specific company."""
    try:
        # Generate unique project ID
        project_id = str(uuid.uuid4())

        # Convert Pydantic model to dict and add required fields
        project_dict = project_data.model_dump()
        project_dict['id'] = project_id
        project_dict['user_id'] = user_id
        project_dict['company_id'] = company_id

        # Create project in database
        created_project = db.create_project(project_dict)

        return ProjectResponse(**created_project)

    except ValueError as e:
        logger.error(f"Validation error creating project: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating project: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create project")


@router.get("/{user_id}/projects", response_model=ProjectListResponse)
async def get_projects(
    user_id: str,
    company_id: str,
    status: Optional[str] = None,
    db: ProjectDatabase = Depends(get_db)
):
    """Get all projects for a user in a specific company, optionally filtered by status."""
    try:
        projects = db.get_projects_by_user_and_company(user_id, company_id, status)

        project_responses = [ProjectResponse(**project) for project in projects]

        return ProjectListResponse(
            projects=project_responses,
            total=len(project_responses)
        )

    except Exception as e:
        logger.error(f"Error getting projects for user {user_id} in company {company_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get projects")


@router.get("/{user_id}/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    user_id: str,
    project_id: str,
    db: ProjectDatabase = Depends(get_db)
):
    """Get a specific project by ID."""
    try:
        project = db.get_project(project_id, user_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return ProjectResponse(**project)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get project")


@router.put("/{user_id}/projects/{project_id}", response_model=ProjectResponse)
async def update_project(
    user_id: str,
    project_id: str,
    updates: UpdateProjectData,
    db: ProjectDatabase = Depends(get_db)
):
    """Update a project."""
    try:
        # Convert Pydantic model to dict, excluding None values
        update_dict = {k: v for k, v in updates.model_dump().items() if v is not None}
        
        if not update_dict:
            # No updates provided, just return current project
            project = db.get_project(project_id, user_id)
            if not project:
                raise HTTPException(status_code=404, detail="Project not found")
            return ProjectResponse(**project)
        
        updated_project = db.update_project(project_id, user_id, update_dict)
        
        if not updated_project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return ProjectResponse(**updated_project)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update project")


@router.delete("/{user_id}/projects/{project_id}")
async def delete_project(
    user_id: str,
    project_id: str,
    db: ProjectDatabase = Depends(get_db)
):
    """Delete a project."""
    try:
        deleted = db.delete_project(project_id, user_id)
        
        if not deleted:
            raise HTTPException(status_code=404, detail="Project not found")
        
        return JSONResponse(
            content={"message": "Project deleted successfully"},
            status_code=200
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete project")


@router.get("/{user_id}/projects/stats", response_model=ProjectStatsResponse)
async def get_project_stats(
    user_id: str,
    db: ProjectDatabase = Depends(get_db)
):
    """Get project statistics for a user."""
    try:
        stats = db.get_project_stats(user_id)
        return ProjectStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Error getting project stats for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get project statistics")


@router.get("/debug/all")
async def debug_list_all_projects(db: ProjectDatabase = Depends(get_db)):
    """Debug endpoint to list all projects in the database."""
    try:
        projects = db.list_all_projects()
        return {"projects": projects, "count": len(projects)}
    except Exception as e:
        logger.error(f"Error in debug endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail="Debug endpoint failed")


@router.get("/health")
async def health_check():
    """Health check endpoint for project management."""
    try:
        db = get_project_db()
        # Simple test to ensure database is accessible
        db.get_project_stats("health_check_user")
        return JSONResponse(
            content={"status": "healthy", "service": "project_management"},
            status_code=200
        )
    except Exception as e:
        logger.error(f"Project management health check failed: {str(e)}")
        return JSONResponse(
            content={"status": "unhealthy", "service": "project_management", "error": str(e)},
            status_code=503
        )
