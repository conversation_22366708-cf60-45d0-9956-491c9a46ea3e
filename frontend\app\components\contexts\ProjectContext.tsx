"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from "react";
import { Project, createNewProject } from "@/app/types/project";
import { useCompanies } from "./CompanyContext";

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
const USER_ID = "default_user"; // TODO: Replace with actual user authentication

// API helper functions
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
    throw new Error(errorData.detail || `HTTP ${response.status}`);
  }

  return response.json();
};

interface ProjectContextType {
  projects: Project[];
  currentProject: Project | null;
  setCurrentProject: (project: Project | null) => void;
  createProject: (name: string) => Promise<Project>;
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  getProjectById: (id: string) => Project | undefined;
  refreshProjects: () => Promise<void>;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
}

const ProjectContext = createContext<ProjectContextType>({
  projects: [],
  currentProject: null,
  setCurrentProject: () => {},
  createProject: async () => createNewProject(""),
  updateProject: async () => {},
  deleteProject: async () => {},
  getProjectById: () => undefined,
  refreshProjects: async () => {},
  loading: false,
  setLoading: () => {},
  error: null,
  setError: () => {},
});

export const useProjects = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error("useProjects must be used within a ProjectProvider");
  }
  return context;
};

export const ProjectProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { currentCompany } = useCompanies();

  // Load projects from API on mount
  const refreshProjects = useCallback(async () => {
    // Don't load projects if no company is selected
    if (!currentCompany) {
      setProjects([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await apiCall(`/projects/${USER_ID}/projects?company_id=${currentCompany.id}`);
      setProjects(response.projects || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load projects';
      setError(errorMessage);
      console.error("Failed to load projects:", err);

      // Fallback to localStorage if API fails
      const savedProjects = localStorage.getItem(`ai360-projects-${currentCompany.id}`);
      if (savedProjects) {
        try {
          const parsedProjects = JSON.parse(savedProjects);
          setProjects(parsedProjects);
        } catch (parseError) {
          console.error("Failed to parse saved projects:", parseError);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [currentCompany]);

  useEffect(() => {
    refreshProjects();
  }, [refreshProjects]);

  // Save projects to localStorage as backup whenever projects change
  useEffect(() => {
    if (projects.length > 0 && currentCompany) {
      localStorage.setItem(`ai360-projects-${currentCompany.id}`, JSON.stringify(projects));
    }
  }, [projects, currentCompany]);

  const createProject = useCallback(async (name: string): Promise<Project> => {
    if (!currentCompany) {
      throw new Error("No company selected");
    }

    try {
      setLoading(true);
      setError(null);

      const projectData = {
        name,
        status: "Preparing",
        privacy: "Private",
        company_id: currentCompany.id
      };

      const response = await apiCall(`/projects/${USER_ID}/projects?company_id=${currentCompany.id}`, {
        method: 'POST',
        body: JSON.stringify(projectData),
      });

      const newProject = response as Project;
      setProjects(prev => [...prev, newProject]);
      return newProject;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create project';
      setError(errorMessage);
      console.error("Failed to create project:", err);

      // Fallback to local creation
      const newProject = createNewProject(name, currentCompany.id);
      setProjects(prev => [...prev, newProject]);
      return newProject;
    } finally {
      setLoading(false);
    }
  }, [currentCompany]);

  const updateProject = useCallback(async (id: string, updates: Partial<Project>) => {
    try {
      setLoading(true);
      setError(null);

      // Remove fields that shouldn't be sent to API
      const { createdAt, updatedAt, ...apiUpdates } = updates;

      const response = await apiCall(`/projects/${USER_ID}/projects/${id}`, {
        method: 'PUT',
        body: JSON.stringify(apiUpdates),
      });

      const updatedProject = response as Project;

      setProjects(prev =>
        prev.map(project =>
          project.id === id ? updatedProject : project
        )
      );

      // Update current project if it's the one being updated
      setCurrentProject(prev =>
        prev?.id === id ? updatedProject : prev
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update project';
      setError(errorMessage);
      console.error("Failed to update project:", err);

      // Fallback to local update
      setProjects(prev =>
        prev.map(project =>
          project.id === id
            ? { ...project, ...updates, updatedAt: new Date().toISOString() }
            : project
        )
      );

      setCurrentProject(prev =>
        prev?.id === id
          ? { ...prev, ...updates, updatedAt: new Date().toISOString() }
          : prev
      );
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProject = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      await apiCall(`/projects/${USER_ID}/projects/${id}`, {
        method: 'DELETE',
      });

      setProjects(prev => prev.filter(project => project.id !== id));

      // Clear current project if it's the one being deleted
      setCurrentProject(prev => prev?.id === id ? null : prev);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete project';
      setError(errorMessage);
      console.error("Failed to delete project:", err);

      // Fallback to local deletion
      setProjects(prev => prev.filter(project => project.id !== id));
      setCurrentProject(prev => prev?.id === id ? null : prev);
    } finally {
      setLoading(false);
    }
  }, []);

  const getProjectById = useCallback((id: string): Project | undefined => {
    return projects.find(project => project.id === id);
  }, [projects]);

  const value: ProjectContextType = {
    projects,
    currentProject,
    setCurrentProject,
    createProject,
    updateProject,
    deleteProject,
    getProjectById,
    refreshProjects,
    loading,
    setLoading,
    error,
    setError,
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
};

export default ProjectContext;
