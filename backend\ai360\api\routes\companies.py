"""
FastAPI routes for company management.
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query

from ai360.api.api_types import (
    CreateCompanyData,
    UpdateCompanyData,
    CompanyResponse,
    CompanyListResponse,
    UserCompanyAccessData,
    UserCompanyAccessResponse
)
from ai360.api.models.company import CompanyDatabase, get_company_db
from ai360.api.core.log import logger

router = APIRouter()


def get_db() -> CompanyDatabase:
    """Dependency to get database instance."""
    return get_company_db()


@router.post("/companies", response_model=CompanyResponse)
async def create_company(
    company_data: CreateCompanyData,
    created_by: str = Query(..., description="User ID who is creating the company"),
    db: CompanyDatabase = Depends(get_db)
):
    """Create a new company."""
    try:
        # Generate unique company ID
        company_id = str(uuid.uuid4())
        
        # Convert Pydantic model to dict and add required fields
        company_dict = company_data.model_dump()
        company_dict['id'] = company_id
        company_dict['created_by'] = created_by
        
        # Validate parent company exists if specified
        if company_dict.get('parent_company_id'):
            parent = db.get_company(company_dict['parent_company_id'])
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Parent company not found"
                )
        
        # Check if company name already exists
        existing = db.get_company_by_name(company_dict['name'])
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Company name already exists"
            )
        
        # Create company in database
        created_company = db.create_company(company_dict)
        
        # Grant creator admin access to the company
        db.grant_user_access(created_by, company_id, 'admin', created_by)
        
        return CompanyResponse(**created_company)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating company: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create company"
        )


@router.get("/companies", response_model=CompanyListResponse)
async def list_companies(
    include_inactive: bool = False,
    db: CompanyDatabase = Depends(get_db)
):
    """List all companies."""
    try:
        companies = db.list_companies(include_inactive=include_inactive)
        return CompanyListResponse(
            companies=[CompanyResponse(**company) for company in companies],
            total=len(companies)
        )
    except Exception as e:
        logger.error(f"Error listing companies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list companies"
        )


@router.get("/companies/{company_id}", response_model=CompanyResponse)
async def get_company(
    company_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Get a company by ID."""
    try:
        company = db.get_company(company_id)
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        return CompanyResponse(**company)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting company {company_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get company"
        )


@router.put("/companies/{company_id}", response_model=CompanyResponse)
async def update_company(
    company_id: str,
    company_data: UpdateCompanyData,
    db: CompanyDatabase = Depends(get_db)
):
    """Update a company."""
    try:
        # Check if company exists
        existing = db.get_company(company_id)
        if not existing:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Convert Pydantic model to dict, excluding None values
        updates = {k: v for k, v in company_data.model_dump().items() if v is not None}
        
        # Validate parent company exists if being updated
        if 'parent_company_id' in updates and updates['parent_company_id']:
            parent = db.get_company(updates['parent_company_id'])
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Parent company not found"
                )
            
            # Prevent circular references
            if updates['parent_company_id'] == company_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Company cannot be its own parent"
                )
        
        # Check if new name already exists (if name is being updated)
        if 'name' in updates and updates['name'] != existing['name']:
            existing_name = db.get_company_by_name(updates['name'])
            if existing_name:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Company name already exists"
                )
        
        # Update company in database
        updated_company = db.update_company(company_id, updates)
        
        return CompanyResponse(**updated_company)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating company {company_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update company"
        )


@router.delete("/companies/{company_id}")
async def delete_company(
    company_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Delete (deactivate) a company."""
    try:
        # Check if company exists
        existing = db.get_company(company_id)
        if not existing:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Check if company has subsidiaries
        subsidiaries = db.get_subsidiary_companies(company_id)
        if subsidiaries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete company with active subsidiaries"
            )
        
        # Soft delete the company
        success = db.delete_company(company_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete company"
            )
        
        return {"message": "Company deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting company {company_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete company"
        )


@router.get("/users/{user_id}/companies", response_model=CompanyListResponse)
async def get_user_companies(
    user_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Get all companies a user has access to."""
    try:
        companies = db.get_companies_for_user(user_id)
        return CompanyListResponse(
            companies=[CompanyResponse(**company) for company in companies],
            total=len(companies)
        )
    except Exception as e:
        logger.error(f"Error getting companies for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user companies"
        )


@router.post("/companies/{company_id}/users", response_model=UserCompanyAccessResponse)
async def grant_user_access(
    company_id: str,
    access_data: UserCompanyAccessData,
    granted_by: str = Query(..., description="User ID who is granting access"),
    db: CompanyDatabase = Depends(get_db)
):
    """Grant a user access to a company."""
    try:
        # Check if company exists
        company = db.get_company(company_id)
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )
        
        # Grant access
        success = db.grant_user_access(
            access_data.user_id,
            company_id,
            access_data.role,
            granted_by
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to grant user access"
            )
        
        # Return access information
        return UserCompanyAccessResponse(
            id=str(uuid.uuid4()),  # This would be the actual access ID from DB
            user_id=access_data.user_id,
            company_id=company_id,
            role=access_data.role,
            granted_at="",  # Would be actual timestamp from DB
            granted_by=granted_by,
            is_active=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error granting user access: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to grant user access"
        )


@router.delete("/companies/{company_id}/users/{user_id}")
async def revoke_user_access(
    company_id: str,
    user_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Revoke a user's access to a company."""
    try:
        # Check if company exists
        company = db.get_company(company_id)
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        # Check if user has access
        if not db.user_has_access(user_id, company_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User does not have access to this company"
            )

        # Revoke access
        success = db.revoke_user_access(user_id, company_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to revoke user access"
            )

        return {"message": "User access revoked successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error revoking user access: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke user access"
        )


@router.get("/companies/{company_id}/subsidiaries", response_model=CompanyListResponse)
async def get_subsidiary_companies(
    company_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Get all subsidiary companies for a parent company."""
    try:
        # Check if parent company exists
        parent = db.get_company(company_id)
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Parent company not found"
            )

        subsidiaries = db.get_subsidiary_companies(company_id)
        return CompanyListResponse(
            companies=[CompanyResponse(**company) for company in subsidiaries],
            total=len(subsidiaries)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting subsidiaries for company {company_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subsidiary companies"
        )


@router.get("/companies/{company_id}/users/{user_id}/access")
async def check_user_access(
    company_id: str,
    user_id: str,
    db: CompanyDatabase = Depends(get_db)
):
    """Check if a user has access to a company and return their role."""
    try:
        # Check if company exists
        company = db.get_company(company_id)
        if not company:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Company not found"
            )

        has_access = db.user_has_access(user_id, company_id)
        role = db.get_user_role(user_id, company_id) if has_access else None

        return {
            "has_access": has_access,
            "role": role,
            "company_id": company_id,
            "user_id": user_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking user access: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check user access"
        )
