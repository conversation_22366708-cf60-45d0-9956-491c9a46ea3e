"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit";
exports.ids = ["vendor-chunks/@dnd-kit"];
exports.modules = {

/***/ "(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js":
/*!*****************************************************!*\
  !*** ./node_modules/@dnd-kit/core/dist/core.esm.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoScrollActivator: () => (/* binding */ AutoScrollActivator),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   DragOverlay: () => (/* binding */ DragOverlay),\n/* harmony export */   KeyboardCode: () => (/* binding */ KeyboardCode),\n/* harmony export */   KeyboardSensor: () => (/* binding */ KeyboardSensor),\n/* harmony export */   MeasuringFrequency: () => (/* binding */ MeasuringFrequency),\n/* harmony export */   MeasuringStrategy: () => (/* binding */ MeasuringStrategy),\n/* harmony export */   MouseSensor: () => (/* binding */ MouseSensor),\n/* harmony export */   PointerSensor: () => (/* binding */ PointerSensor),\n/* harmony export */   TouchSensor: () => (/* binding */ TouchSensor),\n/* harmony export */   TraversalOrder: () => (/* binding */ TraversalOrder),\n/* harmony export */   applyModifiers: () => (/* binding */ applyModifiers),\n/* harmony export */   closestCenter: () => (/* binding */ closestCenter),\n/* harmony export */   closestCorners: () => (/* binding */ closestCorners),\n/* harmony export */   defaultAnnouncements: () => (/* binding */ defaultAnnouncements),\n/* harmony export */   defaultCoordinates: () => (/* binding */ defaultCoordinates),\n/* harmony export */   defaultDropAnimation: () => (/* binding */ defaultDropAnimationConfiguration),\n/* harmony export */   defaultDropAnimationSideEffects: () => (/* binding */ defaultDropAnimationSideEffects),\n/* harmony export */   defaultKeyboardCoordinateGetter: () => (/* binding */ defaultKeyboardCoordinateGetter),\n/* harmony export */   defaultScreenReaderInstructions: () => (/* binding */ defaultScreenReaderInstructions),\n/* harmony export */   getClientRect: () => (/* binding */ getClientRect),\n/* harmony export */   getFirstCollision: () => (/* binding */ getFirstCollision),\n/* harmony export */   getScrollableAncestors: () => (/* binding */ getScrollableAncestors),\n/* harmony export */   pointerWithin: () => (/* binding */ pointerWithin),\n/* harmony export */   rectIntersection: () => (/* binding */ rectIntersection),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useDndMonitor: () => (/* binding */ useDndMonitor),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDroppable: () => (/* binding */ useDroppable),\n/* harmony export */   useSensor: () => (/* binding */ useSensor),\n/* harmony export */   useSensors: () => (/* binding */ useSensors)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/accessibility */ \"(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\");\n\n\n\n\n\nconst DndMonitorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndMonitorContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Set());\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = (0,_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.useAnnouncement)();\n  const liveRegionId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndLiveRegion\");\n  const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor((0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(node) || (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isSVGElement)(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element;\n  }\n\n  if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isNode)(element)) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(element) || element === (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(element).scrollingElement) {\n    return window;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target);\n  return target instanceof EventTarget ? target : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target));\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.initialCoordinates = (_getEventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useInterval)();\n  const scrollSpeed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const autoScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(delta);\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(containers);\n  const disabled = isDisabled();\n  const disabledRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(disabled);\n  const measureDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const droppableRects = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const mutationObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const resizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(node);\n  const ancestors = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const prevElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(firstElement) : null);\n  const [rects, setRects] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(entries => {\n    for (const {\n      target\n    } of entries) {\n      if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previousActivatorEvent = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(activatorEvent);\n  const previousActiveId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.findFirstFocusableNode)(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    initial: null,\n    translated: null\n  });\n  const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [activeSensor, setActiveSensor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [activatorEvent, setActivatorEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const latestProps = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(props, Object.values(props));\n  const draggableDescribedById = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => activatorEvent ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const instantiateSensor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: internalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PublicContext.Provider, {\n    value: publicContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const [activatorNode, setActivatorNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    disabled\n  });\n  const resizeObserverConnected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const callbackId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousChildren = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children, clonedChildren ? (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(initial)\n  }, {\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NullifiedContextProvider, null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\n\n//# sourceMappingURL=core.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@dnd-kit/sortable/dist/sortable.esm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SortableContext: () => (/* binding */ SortableContext),\n/* harmony export */   arrayMove: () => (/* binding */ arrayMove),\n/* harmony export */   arraySwap: () => (/* binding */ arraySwap),\n/* harmony export */   defaultAnimateLayoutChanges: () => (/* binding */ defaultAnimateLayoutChanges),\n/* harmony export */   defaultNewIndexGetter: () => (/* binding */ defaultNewIndexGetter),\n/* harmony export */   hasSortableData: () => (/* binding */ hasSortableData),\n/* harmony export */   horizontalListSortingStrategy: () => (/* binding */ horizontalListSortingStrategy),\n/* harmony export */   rectSortingStrategy: () => (/* binding */ rectSortingStrategy),\n/* harmony export */   rectSwappingStrategy: () => (/* binding */ rectSwappingStrategy),\n/* harmony export */   sortableKeyboardCoordinates: () => (/* binding */ sortableKeyboardCoordinates),\n/* harmony export */   useSortable: () => (/* binding */ useSortable),\n/* harmony export */   verticalListSortingStrategy: () => (/* binding */ verticalListSortingStrategy)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n\n\n\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\n\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\n\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\n\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\n\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\n\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\n\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDndContext)();\n  const containerId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(index);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getClientRect)(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\n\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDraggable)({\n    id,\n    data,\n    attributes: { ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useCombinedRefs)(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n\n  function getTransition() {\n    if ( // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform || // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n\n    if (shouldDisplaceDragSource && !(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent) || !transition) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({ ...transition,\n        property: transitionProperty\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\n\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n\n  return false;\n}\n\nconst directions = [_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n      }\n    });\n    const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.closestCorners)({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getFirstCollision)(collisions, 'id');\n\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getScrollableAncestors)(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\n\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n\n\n//# sourceMappingURL=sortable.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@dnd-kit/utilities/dist/utilities.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSS: () => (/* binding */ CSS),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   findFirstFocusableNode: () => (/* binding */ findFirstFocusableNode),\n/* harmony export */   getEventCoordinates: () => (/* binding */ getEventCoordinates),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasViewportRelativeCoordinates: () => (/* binding */ hasViewportRelativeCoordinates),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isKeyboardEvent: () => (/* binding */ isKeyboardEvent),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isSVGElement: () => (/* binding */ isSVGElement),\n/* harmony export */   isTouchEvent: () => (/* binding */ isTouchEvent),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   useCombinedRefs: () => (/* binding */ useCombinedRefs),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLatestValue: () => (/* binding */ useLatestValue),\n/* harmony export */   useLazyMemo: () => (/* binding */ useLazyMemo),\n/* harmony export */   useNodeRef: () => (/* binding */ useNodeRef),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useUniqueId: () => (/* binding */ useUniqueId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\n\n//# sourceMappingURL=utilities.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\n");

/***/ })

};
;